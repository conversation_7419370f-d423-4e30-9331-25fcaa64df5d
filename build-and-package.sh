#!/bin/bash

# Enhanced Docker build and packaging script with configuration support for Linux
# This script builds the Docker images and packages them with configuration files

set -e  # Exit on any error

# Default values
CONFIG_FILE="deployment-config.json"
NO_BUILD=false
HELP=false

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show help
show_help() {
    echo "Usage: ./build-and-package.sh [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -c, --config FILE     Path to the deployment configuration file (default: deployment-config.json)"
    echo "  -n, --no-build       Skip the build process and only package existing images"
    echo "  -h, --help           Show this help message"
    echo ""
    echo "This script will:"
    echo "  1. Build Docker images (unless --no-build is specified)"
    echo "  2. Save images as .tar files"
    echo "  3. Create a deployment package with docker-compose.yml and config file"
    echo ""
    echo "Examples:"
    echo "  ./build-and-package.sh                           # Build with default config"
    echo "  ./build-and-package.sh -c config.prod.json      # Build with specific config"
    echo "  ./build-and-package.sh --no-build               # Package only, skip build"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -n|--no-build)
            NO_BUILD=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_color $RED "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Check if configuration file exists
if [[ ! -f "$CONFIG_FILE" ]]; then
    print_color $RED "Configuration file '$CONFIG_FILE' not found!"
    print_color $YELLOW "Please ensure the configuration file exists or specify a different path with -c parameter."
    exit 1
fi

print_color $GREEN "=== Verazial Admin Docker Build and Package Script ==="
print_color $YELLOW "Configuration file: $CONFIG_FILE"

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    print_color $RED "Docker is not installed or not in PATH!"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_color $RED "Docker daemon is not running or you don't have permission to access it!"
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_color $RED "Docker Compose is not installed!"
    exit 1
fi

# Determine which docker compose command to use
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

if [[ "$NO_BUILD" != true ]]; then
    # Step 1: Build the images in the current directory
    print_color $YELLOW "Building Docker images..."
    $DOCKER_COMPOSE build --no-cache
    
    if [[ $? -ne 0 ]]; then
        print_color $RED "Docker build failed!"
        exit 1
    fi
fi

# Step 2: Get image names and tags from the current directory
print_color $YELLOW "Getting image information from docker-compose.yml..."
COMPOSE_FILE="docker-compose.yml"

if [[ ! -f "$COMPOSE_FILE" ]]; then
    print_color $RED "docker-compose.yml not found!"
    exit 1
fi

# Extract image names from docker-compose config
IMAGE_IDS=($($DOCKER_COMPOSE config | grep -E "^\s*image:" | sed 's/.*image: *//' | tr -d '"'))

print_color $CYAN "Found images:"
for image_id in "${IMAGE_IDS[@]}"; do
    print_color $CYAN "  - $image_id"
done

print_color $YELLOW "Processing image names..."
SAVED_IMAGES=()

for image_id in "${IMAGE_IDS[@]}"; do
    print_color $CYAN "Processing image: $image_id"
    
    # Extract image name without tag
    image_name=$(echo "$image_id" | sed 's/:.*$//')
    
    # Extract the actual image name (remove group, if exists)
    if [[ "$image_name" == *"/"* ]]; then
        processed_name=$(basename "$image_name")
    else
        processed_name="$image_name"
    fi

    # Check if the image is locally built (matches "verazial/*")
    if [[ "$image_id" =~ ^verazial/.+ ]]; then
        print_color $GREEN "Skipping pull for locally built image $image_id."
    else
        print_color $YELLOW "Pulling external image $image_id..."
        if ! docker pull "$image_id"; then
            print_color $YELLOW "Warning: Failed to pull $image_id. Continuing with local version if available."
        fi
    fi

    # Save the image locally with a clean tar filename
    tar_file="${processed_name}.tar"
    print_color $YELLOW "Saving image $image_id as $tar_file..."
    
    if docker save "$image_id" -o "$tar_file"; then
        SAVED_IMAGES+=("$tar_file")
        print_color $GREEN "Successfully saved $tar_file"
    else
        print_color $RED "Failed to save $image_id"
        exit 1
    fi
done

# Step 3: Create deployment package
print_color $YELLOW "Creating deployment package..."

DEPLOYMENT_DIR="deployment-package"
if [[ -d "$DEPLOYMENT_DIR" ]]; then
    rm -rf "$DEPLOYMENT_DIR"
fi
mkdir -p "$DEPLOYMENT_DIR"

# Copy necessary files to deployment package
cp "docker-compose.yml" "$DEPLOYMENT_DIR/"
cp "$CONFIG_FILE" "$DEPLOYMENT_DIR/deployment-config.json"

# Copy deployment scripts
cp "deploy.sh" "$DEPLOYMENT_DIR/" 2>/dev/null || echo "deploy.sh not found, skipping..."
chmod +x "$DEPLOYMENT_DIR/deploy.sh" 2>/dev/null || true

# Copy all saved images
for tar_file in "${SAVED_IMAGES[@]}"; do
    if [[ -f "$tar_file" ]]; then
        cp "$tar_file" "$DEPLOYMENT_DIR/"
        print_color $GREEN "Added $tar_file to deployment package"
    fi
done

# Create deployment instructions
cat > "$DEPLOYMENT_DIR/README.md" << 'EOF'
# Verazial Admin Deployment Package

## Files included:
- docker-compose.yml: Docker Compose configuration
- deployment-config.json: Application configuration (modify as needed)
- *.tar: Docker images
- deploy.sh: Linux deployment script

## Deployment Instructions:

### Prerequisites:
- Docker installed and running
- Docker Compose installed
- jq installed (for JSON processing): `sudo apt-get install jq` or `sudo yum install jq`

### 1. Configure your environment:
Edit 'deployment-config.json' to match your target environment:
```json
{
  "grpcApiGateway": "https://your-grpc-gateway.com",
  "konektorAPI": "https://your-konektor-api.com",
  "credentialsAPI": "https://your-credentials-api.com/api/v1/",
  "credential_user": "your-username",
  "credential_password": "your-password",
  "environment_name": "PROD",
  "port": "80"
}
```

### 2. Make deployment script executable:
```bash
chmod +x deploy.sh
```

### 3. Deploy the application:
```bash
# Deploy with default configuration
./deploy.sh

# Deploy with specific configuration
./deploy.sh -c deployment-config.json

# Deploy without loading images (if already loaded)
./deploy.sh --no-load-images

# Show help
./deploy.sh --help
```

### 4. Verify deployment:
- Check container status: `docker compose ps`
- View logs: `docker compose logs -f`
- Access application at: http://localhost:[PORT]

## Manual Deployment (if script fails):

### 1. Load Docker images:
```bash
docker load -i admin.tar
# Load any other .tar files if present
```

### 2. Set environment variables:
```bash
export PORT=8080  # or your desired port
```

### 3. Deploy the application:
```bash
docker compose up -d
```

## Configuration Details:

The application will automatically read the configuration from 'deployment-config.json' 
at startup and apply the settings to the running application.

You can modify the configuration file and restart the container to apply changes:
```bash
docker compose restart
```

## Troubleshooting:

- If the container fails to start, check logs: `docker compose logs`
- Ensure all required ports are available: `netstat -tuln | grep :PORT`
- Verify the configuration file is valid JSON: `jq . deployment-config.json`
- Check that all API endpoints in the config are accessible
- Ensure Docker daemon is running: `sudo systemctl status docker`

## Useful Commands:

```bash
# Check container status
docker compose ps

# View application logs
docker compose logs -f angular-app

# Restart application
docker compose restart

# Stop application
docker compose down

# Update configuration and restart
# 1. Edit deployment-config.json
# 2. docker compose restart
```

EOF

# Add generation timestamp
echo "" >> "$DEPLOYMENT_DIR/README.md"
echo "Generated on: $(date)" >> "$DEPLOYMENT_DIR/README.md"

echo ""
print_color $GREEN "=== Build and Package Completed Successfully! ==="
echo ""
print_color $CYAN "Deployment package created in: $DEPLOYMENT_DIR"
echo ""
print_color $YELLOW "Package contents:"
ls -la "$DEPLOYMENT_DIR" | while read line; do
    if [[ "$line" != total* ]]; then
        filename=$(echo "$line" | awk '{print $9}')
        size=$(echo "$line" | awk '{print $5}')
        if [[ "$filename" != "." && "$filename" != ".." && "$filename" != "" ]]; then
            if [[ -d "$DEPLOYMENT_DIR/$filename" ]]; then
                print_color $CYAN "  - $filename (DIR)"
            else
                size_mb=$(echo "scale=2; $size / 1024 / 1024" | bc -l 2>/dev/null || echo "N/A")
                print_color $CYAN "  - $filename (${size_mb} MB)"
            fi
        fi
    fi
done

echo ""
print_color $YELLOW "Next steps:"
print_color $WHITE "1. Copy the '$DEPLOYMENT_DIR' folder to your target server"
print_color $WHITE "2. Edit 'deployment-config.json' for your environment"  
print_color $WHITE "3. Run: chmod +x deploy.sh && ./deploy.sh"
echo ""
