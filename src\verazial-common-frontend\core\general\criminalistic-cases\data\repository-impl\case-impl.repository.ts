import {Injectable} from "@angular/core";
import {environment} from "src/environments/environment";
import {FailureResponse} from "src/verazial-common-frontend/core/classes/failure-response.model";
import {OffsetLimit, StringParam} from "src/verazial-common-frontend/core/generated/util_pb";
import {GrpcStreamInterceptor} from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";
import {Empty} from "google-protobuf/google/protobuf/empty_pb";
import {GrpcLicenseStreamInterceptor} from "src/verazial-common-frontend/core/helpers/grpc-license-stream.interceptor";
import {HttpClient} from "@angular/common/http";
import {
    CaseRepository
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/repository/case.repository";
import {
    CaseGrpcMapper
} from "src/verazial-common-frontend/core/general/criminalistic-cases/data/mapper/case-grpc.mapper";
import {
    GetCasesRequestEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/get-cases-request.entity";
import {CaseEntity} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/case.entity";
import {
    CoreCriminalisticsServiceClient
} from "src/verazial-common-frontend/core/generated/criminalistics/CriminalisticsServiceClientPb";
import {
    CaseCoincidenceGrpcModel,
    CaseCommentGrpcModel,
    CaseEvidenceGrpcModel,
    CaseGrpcModel
} from "src/verazial-common-frontend/core/generated/criminalistics/criminalistics_pb";
import {LocalStorageService} from "src/verazial-common-frontend/core/services/local-storage.service";
import {EncryptionService} from "src/verazial-common-frontend/core/services/encryptionService";
import {
    GetCaseByIdRequestEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/get-case-by-id-request.entity";
import {
    CaseEvidenceEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseEvidence.entity";
import {
    CaseEvidenceGrpcMapper
} from "src/verazial-common-frontend/core/general/criminalistic-cases/data/mapper/case-evidence-grpc.mapper";
import {
    GetEvidenceByCaseIdRequestEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/get-evidence-by-case-id-request.entity";
import {SuccessResponse} from "src/verazial-common-frontend/core/models/success-response.interface";
import {
    CaseDetailEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseDetail.entity";
import {
    toArrayOfCaseDetailEntities,
    toArrayOfCaseDetails,
    toArrayOfLocations,
    toArrayOfLocationsEntities,
    toArrayOfRelatedCases,
    toArrayOfRelatedCasesEntities,
    toArrayOfRelatedSubjects,
    toArrayOfRelatedSubjectsEntities
} from "src/verazial-common-frontend/core/general/criminalistic-cases/common/converter/criminalistic-case.converter";
import {
    RelatedCaseEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/relatedCase.entity";
import {
    RelatedSubjectEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/relatedSubject.entity";
import {
    LocationEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/location.entity";
import {
    CaseGeneralInfoEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseGeneralInfo.entity";
import {
    CaseGeneralInfoGrpcMapper
} from "src/verazial-common-frontend/core/general/criminalistic-cases/data/mapper/case-general-info-grpc.mapper";
import {
    CaseCommentGrpcMapper
} from "src/verazial-common-frontend/core/general/criminalistic-cases/data/mapper/case-comment-grpc.mapper";
import {
    GetCommentByCaseIdRequestEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/get-comment-by-case-id-request.entity";
import {
    CaseCommentEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseComment.entity";
import {
    CaseCoincidenceEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseCoincidence.entity";
import {
    GetCoincidenceByCaseIdRequestEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/get-coincidence-by-case-id-request.entity";
import {
    CaseCoincidenceGrpcMapper
} from "src/verazial-common-frontend/core/general/criminalistic-cases/data/mapper/case-coincidence-grpc.mapper";


@Injectable({
    providedIn: 'root',
})
export class CaseRepositoryImpl extends CaseRepository {
    caseMapper = new CaseGrpcMapper();
    caseGeneralInfoMapper = new CaseGeneralInfoGrpcMapper();
    caseEvidenceMapper = new CaseEvidenceGrpcMapper();
    caseCommentMapper = new CaseCommentGrpcMapper();
    caseCoincidenceMapper = new CaseCoincidenceGrpcMapper();
    localStorageService = new LocalStorageService(new EncryptionService());

    constructor(
        private httpClient: HttpClient,
    ) {
        super();
    }

    /**
     * Get all Cases with offset and limit
     * @param params offset: number; limit: number;
     * @returns list of CaseEntity
     */
    override getAllCases(params: GetCasesRequestEntity): Promise<CaseEntity[]> {
        const request = new OffsetLimit();
        if (params.offset) request.setOffset(params.offset);
        if (params.limit) request.setLimit(params.limit);

        const caseEntities: CaseEntity[] = [];

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        const grpc = coreCriminalisticsServiceClient.getAllCases(request);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: CaseGrpcModel) => {
                caseEntities.push(this.caseMapper.mapFrom(response));
            });

            grpc.on('error', (err) => {
                const failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(caseEntities)
            });
        });
    }

    /**
     * Get total number of cases
     * @returns Promise<number>
     */
    override getNumberOfCases(): Promise<number> {
        const request = new Empty();

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.getNumberOfCases(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(response.getValue());
                    }
                }
            });
        });
    }

    /**
     * Add a new case
     * @param params CaseEntity
     * @returns Promise<CaseEntity>
     */
    override addCase(params: CaseEntity): Promise<CaseEntity> {
        const request = this.caseMapper.mapTo(params);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.createCase(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(this.caseMapper.mapFrom(response.getCase()!));
                    }
                }
            });
        });
    }

    /**
     * Update an existing case
     * @param params CaseEntity
     * @returns Promise<CaseEntity>
     */
    override updateCase(params: CaseEntity): Promise<CaseEntity> {
        const request = this.caseMapper.mapTo(params);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.updateCase(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(this.caseMapper.mapFrom(response.getCase()!));
                    }
                }
            });
        });
    }

    /**
     * Update general info from an existing case
     * @param params CaseGeneralInfoEntity
     * @returns Promise<CaseGeneralInfoEntity>
     */
    override updateCaseGeneralInfo(params: CaseGeneralInfoEntity): Promise<CaseGeneralInfoEntity> {
        const request = this.caseGeneralInfoMapper.mapTo(params);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.updateCaseGeneralInfo(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(this.caseGeneralInfoMapper.mapFrom(response.getCase()!));
                    }
                }
            });
        });
    }

    /**
     * Update details of an existing case
     * @param params CaseDetailEntity[]
     * @returns Promise<CaseDetailEntity[]>
     */
    override updateCaseDetails(params: CaseDetailEntity[]): Promise<CaseDetailEntity[]> {
        const request = toArrayOfCaseDetails(params);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.updateCaseDetails(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(toArrayOfCaseDetailEntities(response.getCasedetails()));
                    }
                }
            });
        });
    }

    /**
     * Update related cases to an existing case
     * @param params RelatedCaseEntity[]
     * @returns Promise<RelatedCaseEntity[]>
     */
    override updateRelatedCases(params: RelatedCaseEntity[]): Promise<RelatedCaseEntity[]> {
        const request = toArrayOfRelatedCases(params);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.updateRelatedCases(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(toArrayOfRelatedCasesEntities(response.getRelatedcases()));
                    }
                }
            });
        });
    }

    /**
     * Update related subjects to an existing case
     * @param params RelatedSubjectEntity[]
     * @returns Promise<RelatedSubjectEntity[]>
     */
    override updateRelatedSubjects(params: RelatedSubjectEntity[]): Promise<RelatedSubjectEntity[]> {
        const request = toArrayOfRelatedSubjects(params);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.updateRelatedSubjects(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(toArrayOfRelatedSubjectsEntities(response.getCaserelatedsubjects()));
                    }
                }
            });
        });
    }

    /**
     * Update existing case locations
     * @param params LocationEntity[]
     * @returns Promise<LocationEntity[]>
     */
    override updateCaseLocations(params: LocationEntity[]): Promise<LocationEntity[]> {
        const request = toArrayOfLocations(params);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.updateCaseLocations(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(toArrayOfLocationsEntities(response.getLocationcases()));
                    }
                }
            });
        });
    }

    /**
     * Get case by Id
     * @param params GetCaseByIdRequestEntity
     * @returns Promise<CaseEntity>
     */
    override getCaseById(params: GetCaseByIdRequestEntity): Promise<CaseEntity> {
        const request = new StringParam();
        request.setParameter(params.id!);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.getCaseById(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(this.caseMapper.mapFrom(response.getCase()!));
                    }
                }
            });
        });
    }

    /**
     * Add evidence to a case
     * @param params CaseEvidenceEntity
     * @returns Promise<CaseEvidenceEntity>
     */
    override addEvidenceCase(params: CaseEvidenceEntity): Promise<CaseEvidenceEntity> {
        const request = this.caseEvidenceMapper.mapTo(params);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.createEvidence(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(this.caseEvidenceMapper.mapFrom(response.getCaseevidence()!));
                    }
                }
            });
        });
    }

    /**
     * Update existing evidence information
     * @param params CaseEvidenceEntity
     * @returns Promise<CaseEvidenceEntity>
     */
    override updateEvidenceCase(params: CaseEvidenceEntity): Promise<CaseEvidenceEntity> {
        const request = this.caseEvidenceMapper.mapTo(params);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.updateEvidence(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(this.caseEvidenceMapper.mapFrom(response.getCaseevidence()!));
                    }
                }
            });
        });
    }

    /**
     * Get evidences by caseId
     * @param params GetEvidenceByIdRequestEntity
     * @returns Promise<CaseEntity>
     */
    override getEvidenceByCaseId(params: GetEvidenceByCaseIdRequestEntity): Promise<CaseEvidenceEntity[]> {
        const request = new StringParam();
        request.setParameter(params.caseId!);

        const caseEvidenceEntities: CaseEvidenceEntity[] = [];

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        const grpc = coreCriminalisticsServiceClient.getEvidenceByCaseId(request);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: CaseEvidenceGrpcModel) => {
                caseEvidenceEntities.push(this.caseEvidenceMapper.mapFrom(response));
            });

            grpc.on('error', (err) => {
                const failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(caseEvidenceEntities)
            });
        });
    }

    /**
     * Delete evidence by id
     * @param params GetCaseByIdRequestEntity
     * @returns Promise<SuccessResponse>
     */
    override deleteEvidenceById(params: GetCaseByIdRequestEntity): Promise<SuccessResponse> {
        const request = new StringParam();
        request.setParameter(params.id!);
        let success!: SuccessResponse;

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.deleteEvidenceById(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }


    /**
     * Delete all locations from a case by id
     * @param params GetCaseByIdRequestEntity
     * @returns Promise<SuccessResponse>
     */
    override deleteAllCaseLocationsById(params: GetCaseByIdRequestEntity): Promise<SuccessResponse> {
        const request = new StringParam();
        request.setParameter(params.id!);
        let success!: SuccessResponse;

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.deleteAllCaseLocationsById(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    /**
     * Delete all related cases from a case by id
     * @param params GetCaseByIdRequestEntity
     * @returns Promise<SuccessResponse>
     */
    override deleteAllRelatedCasesById(params: GetCaseByIdRequestEntity): Promise<SuccessResponse> {
        const request = new StringParam();
        request.setParameter(params.id!);
        let success!: SuccessResponse;

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.deleteAllRelatedCasesById(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    /**
     * Delete all related subjects from a case by id
     * @param params GetCaseByIdRequestEntity
     * @returns Promise<SuccessResponse>
     */
    override deleteAllRelatedSubjectsById(params: GetCaseByIdRequestEntity): Promise<SuccessResponse> {
        const request = new StringParam();
        request.setParameter(params.id!);
        let success!: SuccessResponse;

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.deleteAllRelatedSubjectsById(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    /**
     * Delete case by id
     * @param params GetCaseByIdRequestEntity
     * @returns Promise<SuccessResponse>
     */
    override deleteCaseById(params: GetCaseByIdRequestEntity): Promise<SuccessResponse> {
        const request = new StringParam();
        request.setParameter(params.id!);
        let success!: SuccessResponse;

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.deleteCaseById(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    /**
     * Add comment to a case
     * @param params CaseCommentEntity
     * @returns Promise<CaseCommentEntity>
     */
    override addCaseComment(params: CaseCommentEntity): Promise<CaseCommentEntity> {
        const request = this.caseCommentMapper.mapTo(params);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.createComment(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(this.caseCommentMapper.mapFrom(response.getCasecomment()!));
                    }
                }
            });
        });
    }

    /**
     * Get comments by caseId
     * @param params GetCommentByCaseIdRequestEntity
     * @returns Promise<CaseCommentEntity>
     */
    override getCommentByCaseId(params: GetCommentByCaseIdRequestEntity): Promise<CaseCommentEntity[]> {
        const request = new StringParam();
        request.setParameter(params.caseId!);

        const caseCommentEntities: CaseCommentEntity[] = [];

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        const grpc = coreCriminalisticsServiceClient.getAllCommentsByCaseId(request);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: CaseCommentGrpcModel) => {
                caseCommentEntities.push(this.caseCommentMapper.mapFrom(response));
            });

            grpc.on('error', (err) => {
                const failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(caseCommentEntities)
            });
        });
    }

    override addCaseCoincidence(params: CaseCoincidenceEntity): Promise<CaseCoincidenceEntity> {
        const request = this.caseCoincidenceMapper.mapTo(params);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.createCoincidence(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(this.caseCoincidenceMapper.mapFrom(response.getCasecoincidence()!));
                    }
                }
            });
        });
    }

    override updateCaseCoincidence(params: CaseCoincidenceEntity): Promise<CaseCoincidenceEntity> {
        const request = this.caseCoincidenceMapper.mapTo(params);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.updateCoincidence(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(this.caseCoincidenceMapper.mapFrom(response.getCasecoincidence()!));
                    }
                }
            });
        });
    }

    override deleteCaseCoincidenceById(params: GetCaseByIdRequestEntity): Promise<SuccessResponse> {
        const request = new StringParam();
        request.setParameter(params.id!);
        let success!: SuccessResponse;

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.deleteCoincidenceById(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    override getAllCaseCoincidenceByCaseId(params: GetCoincidenceByCaseIdRequestEntity): Promise<CaseCoincidenceEntity[]> {
        const request = new StringParam();
        request.setParameter(params.caseId!);

        const caseCoincidenceEntities: CaseCoincidenceEntity[] = [];

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        const grpc = coreCriminalisticsServiceClient.getAllCoincidencesByCaseId(request);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: CaseCoincidenceGrpcModel) => {
                caseCoincidenceEntities.push(this.caseCoincidenceMapper.mapFrom(response));
            });

            grpc.on('error', (err) => {
                const failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(caseCoincidenceEntities)
            });
        });
    }

    override getCoincidenceById(params: GetCaseByIdRequestEntity): Promise<CaseCoincidenceEntity> {
        const request = new StringParam();
        request.setParameter(params.id!);

        const coreCriminalisticsServiceClient = new CoreCriminalisticsServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            {'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()]});

        return new Promise((resolve, reject) => {
            coreCriminalisticsServiceClient.getCoincidenceById(request, null, (err, response) => {
                if (err) {
                    const failure: FailureResponse = {
                        code: err.code,
                        message: err.message
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        const failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        };
                        reject(failure);
                    } else {
                        resolve(this.caseCoincidenceMapper.mapFrom(response.getCasecoincidence()!));
                    }
                }
            });
        });
    }
}