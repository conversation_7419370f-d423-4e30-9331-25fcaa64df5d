import {Component, EventEmitter, Input, OnInit, Output} from "@angular/core";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {PrimeNGConfig} from "primeng/api";
import {FileSelectEvent, FileUploadHandlerEvent} from "primeng/fileupload";
import {ConsoleLoggerService} from "src/verazial-common-frontend/core/services/console-logger.service";
import {
    CaseEvidenceEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseEvidence.entity";
import {ValidatorService} from "src/verazial-common-frontend/modules/shared/services/validator.service";

@Component({
    selector: 'app-upload-evidence-files',
    templateUrl: './upload-evidence-files.component.html',
    styleUrl: './upload-evidence-files.component.css',
})
export class UploadEvidenceFilesComponent implements OnInit {

    // Inputs
    @Input() readAndWritePermissions: boolean = false;
    @Input() showUploadDialog: boolean = false;
    @Input() showForm: boolean = true;
    @Input() acceptedFiles: string = 'image/*';
    @Input() maxFileSize: string = '10400000'; // 10MB
    // Outputs
    @Output() onCancel = new EventEmitter<void>();
    @Output() onUpload = new EventEmitter<{
        event: FileUploadHandlerEvent,
        file: CaseEvidenceEntity,
    }>();
    @Output() onSelect = new EventEmitter<FileSelectEvent>();

    file: CaseEvidenceEntity = new CaseEvidenceEntity();

    maxDate: Date = new Date();

    public form: FormGroup = this.fb.group({
        name: ['', [Validators.required]],
        location: ['', [Validators.required]],
        obtainedDate: ['', [Validators.required]],
    });

    constructor(
        private fb: FormBuilder,
        private config: PrimeNGConfig,
        private loggerService: ConsoleLoggerService,
        private validatorService: ValidatorService,
    ) {
    }

    ngOnInit(): void {
    }

    onUploadFiles(event: FileUploadHandlerEvent) {
        this.form.markAllAsTouched();
        if (this.form.valid) {
            const file = event.files[0];
            if (this.showForm) {
                this.file.name = this.form.get('name')?.value || file.name.substring(0, file.name.lastIndexOf('.')) || file.name;
                this.file.location = this.form.get('location')?.value || '';
                this.file.obtainedAt = this.form.get('obtainedDate')?.value || '';
            }

            this.onUpload.emit({event: event, file: this.file});
        }
    }

    onSelectedFiles(event: FileSelectEvent) {
        this.loggerService.debug(event);
        const file = event.currentFiles[0];
        if (this.showForm) {
            if (this.form.get('name')?.value == '' || !this.form.get('name')?.value) {
                this.form.get('name')?.setValue(file.name.substring(0, file.name.lastIndexOf('.')) || file.name);
            }
        }
        this.file.mimeType = file.type;

        this.onSelect.emit(event);
    }

    onCancelDialog() {
        this.form.reset();
        this.file = new CaseEvidenceEntity();
        this.onCancel.emit();
    }

    formatSize(bytes: number) {
        const k = 1024;
        const dm = 3;
        const sizes = this.config.translation.fileSizeTypes || [];
        if (bytes === 0) {
            return `0 ${sizes[0]}`;
        }

        const i = Math.floor(Math.log(bytes) / Math.log(k));
        const formattedSize = parseFloat((bytes / Math.pow(k, i)).toFixed(dm));

        return `${formattedSize} ${sizes[i]}`;
    }

    isValid(field: string): boolean {
        return this.validatorService.isValidField(this.form, field);
    }

    isRequiredField(field: string): boolean {
        return this.validatorService.isRequiredField(this.form, field);
    }
}