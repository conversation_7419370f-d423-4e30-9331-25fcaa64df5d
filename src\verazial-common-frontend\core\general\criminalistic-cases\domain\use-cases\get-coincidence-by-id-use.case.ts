import {UseCaseGrpc} from "src/verazial-common-frontend/core/use-case-grpc";
import {
    CaseRepository
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/repository/case.repository";
import {
    CaseCoincidenceEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseCoincidence.entity";
import {
    GetCaseByIdRequestEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/get-case-by-id-request.entity";

export class GetCoincidenceByIdUseCase implements UseCaseGrpc<GetCaseByIdRequestEntity, CaseCoincidenceEntity> {
    constructor(private caseRepository: CaseRepository) {
    }

    execute(params: GetCaseByIdRequestEntity): Promise<CaseCoincidenceEntity> {
        return this.caseRepository.getCoincidenceById(params);
    }
}