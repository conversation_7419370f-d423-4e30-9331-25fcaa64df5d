version: '3.8'

services:
  angular-app:
    container_name: verazial-admin-app
    image: verazial/admin:v2.0.16
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "${PORT:-20002}:80"
    restart: unless-stopped
    volumes:
      - ./deployment-config.json:/app/config/deployment-config.json:ro
    networks:
      - verazial-network
    environment:
      - NODE_ENV=production

networks:
  verazial-network:
    external: true
