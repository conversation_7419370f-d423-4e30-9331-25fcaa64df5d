# Deployment script for Verazial Admin
# This script should be placed in the deployment package and run on the target server

param(
    [string]$ConfigFile = "deployment-config.json",
    [switch]$LoadImages = $true,
    [switch]$Help = $false
)

if ($Help) {
    Write-Host "Usage: .\deploy.ps1 [-ConfigFile <path>] [-LoadImages] [-Help]"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "  -ConfigFile   Path to the deployment configuration file (default: deployment-config.json)"
    Write-Host "  -LoadImages   Load Docker images from .tar files (default: true)"
    Write-Host "  -Help         Show this help message"
    exit 0
}

Write-Host "=== Verazial Admin Deployment Script ===" -ForegroundColor Green

# Check if configuration file exists
if (-not (Test-Path $ConfigFile)) {
    Write-Host "Configuration file '$ConfigFile' not found!" -ForegroundColor Red
    Write-Host "Please ensure the configuration file exists in the current directory."
    exit 1
}

# Validate configuration file
try {
    $config = Get-Content $ConfigFile | ConvertFrom-Json
    Write-Host "Configuration loaded successfully:" -ForegroundColor Green
    Write-Host "  - gRPC API Gateway: $($config.grpcApiGateway)" -ForegroundColor Cyan
    Write-Host "  - Konektor API: $($config.konektorAPI)" -ForegroundColor Cyan
    Write-Host "  - Credentials API: $($config.credentialsAPI)" -ForegroundColor Cyan
    Write-Host "  - Port: $($config.port)" -ForegroundColor Cyan
} catch {
    Write-Host "Error reading configuration file: $_" -ForegroundColor Red
    exit 1
}

# Set environment variable for port
$env:PORT = $config.port
Write-Host "Set PORT environment variable to: $($config.port)" -ForegroundColor Yellow

if ($LoadImages) {
    # Load Docker images
    Write-Host "Loading Docker images..." -ForegroundColor Yellow
    $tarFiles = Get-ChildItem -Filter "*.tar"
    
    if ($tarFiles.Count -eq 0) {
        Write-Host "No .tar files found in current directory!" -ForegroundColor Red
        exit 1
    }
    
    foreach ($tarFile in $tarFiles) {
        Write-Host "Loading image from $($tarFile.Name)..." -ForegroundColor Cyan
        docker load -i $tarFile.Name
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Successfully loaded $($tarFile.Name)" -ForegroundColor Green
        } else {
            Write-Host "Failed to load $($tarFile.Name)" -ForegroundColor Red
            exit 1
        }
    }
}

# Check if docker-compose.yml exists
if (-not (Test-Path "docker-compose.yml")) {
    Write-Host "docker-compose.yml not found in current directory!" -ForegroundColor Red
    exit 1
}

# Stop existing containers
Write-Host "Stopping existing containers..." -ForegroundColor Yellow
docker compose down

# Start the application
Write-Host "Starting Verazial Admin application..." -ForegroundColor Yellow
docker compose up -d

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "=== Deployment Completed Successfully! ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "Application is now running on port: $($config.port)" -ForegroundColor Cyan
    Write-Host "Access URL: http://localhost:$($config.port)" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "To check status: docker compose ps" -ForegroundColor Yellow
    Write-Host "To view logs: docker compose logs -f" -ForegroundColor Yellow
    Write-Host "To stop: docker compose down" -ForegroundColor Yellow
} else {
    Write-Host "Deployment failed! Check the logs with: docker compose logs" -ForegroundColor Red
    exit 1
}
