import {Mapper} from "src/verazial-common-frontend/core/mapper";
import {dateToTimestamp} from "src/verazial-common-frontend/core/util/date-to-timestamp";
import {CaseCoincidenceGrpcModel} from "src/verazial-common-frontend/core/generated/criminalistics/criminalistics_pb"
import {
    CaseCoincidenceEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseCoincidence.entity";

export class CaseCoincidenceGrpcMapper extends Mapper<CaseCoincidenceGrpcModel, CaseCoincidenceEntity> {
    override mapFrom(param: CaseCoincidenceGrpcModel): CaseCoincidenceEntity {
        return {
            id: param.getId(),
            caseId: param.getCaseid(),
            evidenceId: param.getEvidenceid(),
            coincidenceSubjectId: param.getCoincidencesubjectid(),
            type: param.getType(),
            status: param.getStatus(),
            score: param.getScore(),
            searchFilters: param.getSearchfilters() ? JSON.parse(param.getSearchfilters()) : {},
            authUserId: param.getAuthuserid(),
            authUserNumId: param.getAuthusernumid(),
            authUserSignatureTech: param.getAuthusersignaturetech(),
            authUserSignatureDate: new Date(param.getAuthusersignaturedate()?.getSeconds()!! * 1000 + Math.round(param.getAuthusersignaturedate()?.getNanos()!! / 1e6)),
            content: param.getContent_asB64(),
            mimeType: param.getMimetype(),
            createdBy: param.getCreatedby(),
            updatedBy: param.getUpdatedby(),
            createdAt: new Date(param.getCreatedat()?.getSeconds()!! * 1000 + Math.round(param.getCreatedat()?.getNanos()!! / 1e6)),
            updatedAt: new Date(param.getUpdatedat()?.getSeconds()!! * 1000 + Math.round(param.getUpdatedat()?.getNanos()!! / 1e6)),
        }
    }

    override mapTo(param: CaseCoincidenceEntity): CaseCoincidenceGrpcModel {
        const caseCoincidenceGrpcModel = new CaseCoincidenceGrpcModel();
        caseCoincidenceGrpcModel.setId(param.id!);
        caseCoincidenceGrpcModel.setCaseid(param.caseId!);
        caseCoincidenceGrpcModel.setEvidenceid(param.evidenceId!);
        caseCoincidenceGrpcModel.setCoincidencesubjectid(param.coincidenceSubjectId!);
        caseCoincidenceGrpcModel.setType(param.type!);
        caseCoincidenceGrpcModel.setStatus(param.status!);
        caseCoincidenceGrpcModel.setScore(param.score!);
        caseCoincidenceGrpcModel.setSearchfilters(JSON.stringify(param.searchFilters!));
        caseCoincidenceGrpcModel.setAuthuserid(param.authUserId!);
        caseCoincidenceGrpcModel.setAuthusernumid(param.authUserNumId!);
        caseCoincidenceGrpcModel.setAuthusersignaturetech(param.authUserSignatureTech!);
        caseCoincidenceGrpcModel.setAuthusersignaturedate(dateToTimestamp(param.authUserSignatureDate!));
        caseCoincidenceGrpcModel.setContent(param.content!);
        caseCoincidenceGrpcModel.setMimetype(param.mimeType!);
        caseCoincidenceGrpcModel.setCreatedby(param.createdBy!);
        caseCoincidenceGrpcModel.setUpdatedby(param.updatedBy!);
        caseCoincidenceGrpcModel.setCreatedat(dateToTimestamp(param.createdAt!));
        caseCoincidenceGrpcModel.setUpdatedat(dateToTimestamp(param.updatedAt!));

        return caseCoincidenceGrpcModel;
    }
}