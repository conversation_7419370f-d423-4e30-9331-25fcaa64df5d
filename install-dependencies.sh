#!/bin/bash

# Installation script for Verazial Admin deployment dependencies on Linux
# This script installs <PERSON><PERSON>, Docker Compose, and jq if they're not already installed

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to detect OS
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VER=$(lsb_release -sr)
    elif [[ -f /etc/redhat-release ]]; then
        OS="Red Hat Enterprise Linux"
        VER=$(cat /etc/redhat-release | sed s/.*release\ // | sed s/\ .*//)
    else
        OS=$(uname -s)
        VER=$(uname -r)
    fi
}

# Function to install Docker on Ubuntu/Debian
install_docker_ubuntu() {
    print_color $YELLOW "Installing Docker on Ubuntu/Debian..."
    
    # Update package index
    sudo apt-get update
    
    # Install packages to allow apt to use a repository over HTTPS
    sudo apt-get install -y \
        ca-certificates \
        curl \
        gnupg \
        lsb-release
    
    # Add Docker's official GPG key
    sudo mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
    
    # Set up the repository
    echo \
        "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
        $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Update package index again
    sudo apt-get update
    
    # Install Docker Engine
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    print_color $GREEN "Docker installed successfully!"
}

# Function to install Docker on CentOS/RHEL/Fedora
install_docker_centos() {
    print_color $YELLOW "Installing Docker on CentOS/RHEL/Fedora..."
    
    # Install required packages
    sudo yum install -y yum-utils
    
    # Add Docker repository
    sudo yum-config-manager \
        --add-repo \
        https://download.docker.com/linux/centos/docker-ce.repo
    
    # Install Docker Engine
    sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    print_color $GREEN "Docker installed successfully!"
}

# Function to install jq
install_jq() {
    print_color $YELLOW "Installing jq..."
    
    if command -v apt-get &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y jq
    elif command -v yum &> /dev/null; then
        sudo yum install -y jq
    elif command -v dnf &> /dev/null; then
        sudo dnf install -y jq
    else
        print_color $RED "Could not install jq automatically. Please install it manually."
        print_color $YELLOW "Visit: https://stedolan.github.io/jq/download/"
        return 1
    fi
    
    print_color $GREEN "jq installed successfully!"
}

# Function to add user to docker group
add_user_to_docker_group() {
    print_color $YELLOW "Adding current user to docker group..."
    sudo usermod -aG docker $USER
    print_color $GREEN "User added to docker group!"
    print_color $YELLOW "Note: You may need to log out and log back in for this to take effect."
    print_color $YELLOW "Or run: newgrp docker"
}

# Main installation function
main() {
    print_color $GREEN "=== Verazial Admin Dependencies Installation ==="
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        print_color $YELLOW "Running as root. This is fine for installation."
    fi
    
    # Detect OS
    detect_os
    print_color $CYAN "Detected OS: $OS $VER"
    
    # Check if Docker is already installed
    if command -v docker &> /dev/null; then
        print_color $GREEN "Docker is already installed: $(docker --version)"
        
        # Check if Docker daemon is running
        if ! docker info &> /dev/null; then
            print_color $YELLOW "Docker daemon is not running. Starting it..."
            sudo systemctl start docker
            sudo systemctl enable docker
        fi
    else
        print_color $YELLOW "Docker not found. Installing Docker..."
        
        case "$OS" in
            *"Ubuntu"*|*"Debian"*)
                install_docker_ubuntu
                ;;
            *"CentOS"*|*"Red Hat"*|*"Fedora"*)
                install_docker_centos
                ;;
            *)
                print_color $RED "Unsupported OS: $OS"
                print_color $YELLOW "Please install Docker manually: https://docs.docker.com/engine/install/"
                exit 1
                ;;
        esac
    fi
    
    # Check if Docker Compose is available
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_color $YELLOW "Docker Compose not found. Installing..."
        
        # Try to install docker-compose-plugin if not available
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y docker-compose-plugin
        elif command -v yum &> /dev/null; then
            sudo yum install -y docker-compose-plugin
        else
            # Fallback to standalone docker-compose
            print_color $YELLOW "Installing standalone docker-compose..."
            sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose
        fi
    else
        print_color $GREEN "Docker Compose is already available"
    fi
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        install_jq
    else
        print_color $GREEN "jq is already installed: $(jq --version)"
    fi
    
    # Add user to docker group if not root
    if [[ $EUID -ne 0 ]]; then
        if ! groups $USER | grep -q docker; then
            add_user_to_docker_group
        else
            print_color $GREEN "User is already in docker group"
        fi
    fi
    
    print_color $GREEN "=== Installation Complete! ==="
    print_color $CYAN "Installed components:"
    print_color $CYAN "  - Docker: $(docker --version)"
    
    if command -v docker-compose &> /dev/null; then
        print_color $CYAN "  - Docker Compose: $(docker-compose --version)"
    elif docker compose version &> /dev/null; then
        print_color $CYAN "  - Docker Compose: $(docker compose version --short)"
    fi
    
    print_color $CYAN "  - jq: $(jq --version)"
    
    echo ""
    print_color $YELLOW "Next steps:"
    print_color $WHITE "1. If you added user to docker group, log out and log back in (or run: newgrp docker)"
    print_color $WHITE "2. Test Docker: docker run hello-world"
    print_color $WHITE "3. You're ready to deploy Verazial Admin!"
    echo ""
}

# Run main function
main "$@"
