#!/bin/bash

# Script to replace environment variables in the built Angular application
# This script runs inside the Docker container at startup

CONFIG_FILE="/app/config/deployment-config.json"
TARGET_DIR="/usr/share/nginx/html"

echo "Starting environment configuration..."

if [ ! -f "$CONFIG_FILE" ]; then
    echo "Configuration file not found at $CONFIG_FILE"
    echo "Using default environment settings"
    exit 0
fi

echo "Reading configuration from $CONFIG_FILE"

# Read configuration values
GRPC_API_GATEWAY=$(jq -r '.grpcApiGateway' "$CONFIG_FILE")
KONEKTOR_API=$(jq -r '.konektorAPI' "$CONFIG_FILE")
CREDENTIALS_API=$(jq -r '.credentialsAPI' "$CONFIG_FILE")
CREDENTIAL_USER=$(jq -r '.credential_user' "$CONFIG_FILE")
CREDENTIAL_PASSWORD=$(jq -r '.credential_password' "$CONFIG_FILE")

echo "Applying configuration:"
echo "  gRPC API Gateway: $GRPC_API_GATEWAY"
echo "  Konektor API: $KONEKTOR_API"
echo "  Credentials API: $CREDENTIALS_API"
echo "  Credential User: $CREDENTIAL_USER"

# Find all JavaScript files in the target directory
find "$TARGET_DIR" -name "*.js" -type f | while read -r file; do
    echo "Processing file: $file"
    
    # Replace the environment variables in the JavaScript files
    # Note: These placeholders will be set in the environment.ts file
    sed -i "s|__GRPC_API_GATEWAY__|$GRPC_API_GATEWAY|g" "$file"
    sed -i "s|__KONEKTOR_API__|$KONEKTOR_API|g" "$file"
    sed -i "s|__CREDENTIALS_API__|$CREDENTIALS_API|g" "$file"
    sed -i "s|__CREDENTIAL_USER__|$CREDENTIAL_USER|g" "$file"
    sed -i "s|__CREDENTIAL_PASSWORD__|$CREDENTIAL_PASSWORD|g" "$file"
done

echo "Environment configuration completed successfully!"
