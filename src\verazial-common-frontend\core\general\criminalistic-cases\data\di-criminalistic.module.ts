import {CommonModule} from "@angular/common";
import {NgModule} from "@angular/core";
import {
    AddCaseUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/add-case.use-case";
import {
    CaseRepository
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/repository/case.repository";
import {
    GetAllCasesUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/get-all-cases.use-case";
import {
    GetNumberOfCasesUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/get-number-of-cases.use-case";
import {
    CaseRepositoryImpl
} from "src/verazial-common-frontend/core/general/criminalistic-cases/data/repository-impl/case-impl.repository";
import {
    GetCaseByIdUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/get-case-by-id-use.case";
import {
    UpdateCaseUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/update-case.use-case";
import {
    AddEvidenceCaseUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/add-evidence-case.use-case";
import {
    GetEvidenceByCaseIdUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/get-evidence-by-case-id-use.case";
import {
    UpdateCaseDetailsUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/update-case-details.use-case";
import {
    UpdateRelatedCasesUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/update-related-cases.use-case";
import {
    UpdateCaseLocationsUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/update-case-locations.use-case";
import {
    UpdateRelatedSubjectsUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/update-related-subjects.use-case";
import {
    DeleteEvidenceByIdUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/delete-evidence-by-id-use.case";
import {
    DeleteCaseByIdUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/delete-case-by-id-use.case";
import {
    DeleteAllCaseLocationsByIdUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/delete-all-case-locations-by-id-use.case";
import {
    DeleteAllRelatedCasesByIdUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/delete-all-related-cases-by-id-use.case";
import {
    DeleteAllRelatedSubjectsByIdUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/delete-all-related-subjects-by-id-use.case";
import {
    UpdateCaseGeneralInfoUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/update-case-general-info.use-case";
import {
    AddCommentCaseUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/add-comment-case.use-case";
import {
    GetCommentByCaseIdUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/get-comment-by-case-id-use.case";
import {
    AddCoincidenceCaseUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/add-coincidence-case.use-case";
import {
    UpdateCaseCoincidenceUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/update-case-coincidence.use-case";
import {
    DeleteCoincidenceByIdUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/delete-coincidence-by-id-use.case";
import {
    GetAllCoincidencesByCaseIdUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/get-all-coincidences-by-case-id-use.case";
import {
    UpdateCaseEvidenceUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/update-evidence.use-case";
import {
    GetCoincidenceByIdUseCase
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/use-cases/get-coincidence-by-id-use.case";

const getAllCasesUseCaseFactory =
    (caseRepository: CaseRepository) => new GetAllCasesUseCase(caseRepository);

const getNumberOfCasesUseCaseFactory =
    (caseRepository: CaseRepository) => new GetNumberOfCasesUseCase(caseRepository);

const addCaseUseCaseFactory =
    (caseRepository: CaseRepository) => new AddCaseUseCase(caseRepository);

const getCaseByIdUseCaseFactory =
    (caseRepository: CaseRepository) => new GetCaseByIdUseCase(caseRepository);

const updateCaseUseCaseFactory =
    (caseRepository: CaseRepository) => new UpdateCaseUseCase(caseRepository);

const updateCaseLocationsUseCaseFactory =
    (caseRepository: CaseRepository) => new UpdateCaseLocationsUseCase(caseRepository);

const updateCaseDetailsUseCaseFactory =
    (caseRepository: CaseRepository) => new UpdateCaseDetailsUseCase(caseRepository);

const updateCaseGeneralInfoUseCaseFactory =
    (caseRepository: CaseRepository) => new UpdateCaseGeneralInfoUseCase(caseRepository);

const updateRelatedCasesUseCaseFactory =
    (caseRepository: CaseRepository) => new UpdateRelatedCasesUseCase(caseRepository);

const updateRelatedSubjectsUseCaseFactory =
    (caseRepository: CaseRepository) => new UpdateRelatedSubjectsUseCase(caseRepository);

const addEvidenceCaseUseCaseFactory =
    (caseRepository: CaseRepository) => new AddEvidenceCaseUseCase(caseRepository);

const updateCaseEvidenceUseCaseFactory =
    (caseRepository: CaseRepository) => new UpdateCaseEvidenceUseCase(caseRepository);

const getEvidenceCaseByCaseIdUseCaseFactory =
    (caseRepository: CaseRepository) => new GetEvidenceByCaseIdUseCase(caseRepository);

const deleteEvidenceCaseByIdUseCaseFactory =
    (caseRepository: CaseRepository) => new DeleteEvidenceByIdUseCase(caseRepository);

const deleteCaseByIdUseCaseFactory =
    (caseRepository: CaseRepository) => new DeleteCaseByIdUseCase(caseRepository);

const deleteAllCaseLocationsByIdUseCaseFactory =
    (caseRepository: CaseRepository) => new DeleteAllCaseLocationsByIdUseCase(caseRepository);

const deleteAllRelatedCasesByIdUseCaseFactory =
    (caseRepository: CaseRepository) => new DeleteAllRelatedCasesByIdUseCase(caseRepository);

const deleteAllRelatedSubjectsByIdUseCaseFactory =
    (caseRepository: CaseRepository) => new DeleteAllRelatedSubjectsByIdUseCase(caseRepository);

const addCommentCaseUseCaseFactory =
    (caseRepository: CaseRepository) => new AddCommentCaseUseCase(caseRepository);

const getCommentCaseByCaseIdUseCaseFactory =
    (caseRepository: CaseRepository) => new GetCommentByCaseIdUseCase(caseRepository);

const addCoincidenceCaseUseCaseFactory =
    (caseRepository: CaseRepository) => new AddCoincidenceCaseUseCase(caseRepository);

const updateCaseCoincidenceUseCaseFactory =
    (caseRepository: CaseRepository) => new UpdateCaseCoincidenceUseCase(caseRepository);

const deleteCaseCoincidenceByIdUseCaseFactory =
    (caseRepository: CaseRepository) => new DeleteCoincidenceByIdUseCase(caseRepository);

const getAllCoincidencesByCaseIdUseCaseFactory =
    (caseRepository: CaseRepository) => new GetAllCoincidencesByCaseIdUseCase(caseRepository);

const getCoincidenceByIdUseCaseFactory =
    (caseRepository: CaseRepository) => new GetCoincidenceByIdUseCase(caseRepository);

export const getAllCasesUseCaseProvider = {
    provide: GetAllCasesUseCase,
    useFactory: getAllCasesUseCaseFactory,
    deps: [CaseRepository]
}

export const getNumberOfCasesUseCaseProvider = {
    provide: GetNumberOfCasesUseCase,
    useFactory: getNumberOfCasesUseCaseFactory,
    deps: [CaseRepository]
}

export const addCaseUseCaseProvider = {
    provide: AddCaseUseCase,
    useFactory: addCaseUseCaseFactory,
    deps: [CaseRepository]
}

export const getCaseByIdUseCaseProvider = {
    provide: GetCaseByIdUseCase,
    useFactory: getCaseByIdUseCaseFactory,
    deps: [CaseRepository]
};

export const updateCaseUseCaseProvider = {
    provide: UpdateCaseUseCase,
    useFactory: updateCaseUseCaseFactory,
    deps: [CaseRepository]
}

export const updateCaseGeneralInfoUseCaseProvider = {
    provide: UpdateCaseGeneralInfoUseCase,
    useFactory: updateCaseGeneralInfoUseCaseFactory,
    deps: [CaseRepository]
}

export const updateCaseLocationsUseCaseProvider = {
    provide: UpdateCaseLocationsUseCase,
    useFactory: updateCaseLocationsUseCaseFactory,
    deps: [CaseRepository]
}

export const updateCaseDetailsUseCaseProvider = {
    provide: UpdateCaseDetailsUseCase,
    useFactory: updateCaseDetailsUseCaseFactory,
    deps: [CaseRepository]
}

export const updateRelatedCasesUseCaseProvider = {
    provide: UpdateRelatedCasesUseCase,
    useFactory: updateRelatedCasesUseCaseFactory,
    deps: [CaseRepository]
}

export const updateRelatedSubjectsUseCaseProvider = {
    provide: UpdateRelatedSubjectsUseCase,
    useFactory: updateRelatedSubjectsUseCaseFactory,
    deps: [CaseRepository]
}

export const addEvidenceCaseUseCaseProvider = {
    provide: AddEvidenceCaseUseCase,
    useFactory: addEvidenceCaseUseCaseFactory,
    deps: [CaseRepository]
}

export const updateCaseEvidenceUseCaseProvider = {
    provide: UpdateCaseEvidenceUseCase,
    useFactory: updateCaseEvidenceUseCaseFactory,
    deps: [CaseRepository]
}

export const getEvidenceCaseByCaseIdUseCaseProvider = {
    provide: GetEvidenceByCaseIdUseCase,
    useFactory: getEvidenceCaseByCaseIdUseCaseFactory,
    deps: [CaseRepository]
}

export const deleteEvidenceCaseByIdUseCaseProvider = {
    provide: DeleteEvidenceByIdUseCase,
    useFactory: deleteEvidenceCaseByIdUseCaseFactory,
    deps: [CaseRepository]
}

export const deleteCaseByIdUseCaseProvider = {
    provide: DeleteCaseByIdUseCase,
    useFactory: deleteCaseByIdUseCaseFactory,
    deps: [CaseRepository]
}

export const deleteAllCaseLocationsByIdUseCaseProvider = {
    provide: DeleteAllCaseLocationsByIdUseCase,
    useFactory: deleteAllCaseLocationsByIdUseCaseFactory,
    deps: [CaseRepository]
}

export const deleteAllRelatedCasesByIdUseCaseProvider = {
    provide: DeleteAllRelatedCasesByIdUseCase,
    useFactory: deleteAllRelatedCasesByIdUseCaseFactory,
    deps: [CaseRepository]
}

export const deleteAllRelatedSubjectsByIdUseCaseProvider = {
    provide: DeleteAllRelatedSubjectsByIdUseCase,
    useFactory: deleteAllRelatedSubjectsByIdUseCaseFactory,
    deps: [CaseRepository]
}

export const addCommentCaseUseCaseProvider = {
    provide: AddCommentCaseUseCase,
    useFactory: addCommentCaseUseCaseFactory,
    deps: [CaseRepository]
}

export const getCommentCaseByCaseIdUseCaseProvider = {
    provide: GetCommentByCaseIdUseCase,
    useFactory: getCommentCaseByCaseIdUseCaseFactory,
    deps: [CaseRepository]
}

export const addCoincidenceCaseUseCaseProvider = {
    provide: AddCoincidenceCaseUseCase,
    useFactory: addCoincidenceCaseUseCaseFactory,
    deps: [CaseRepository]
}

export const updateCaseCoincidenceUseCaseProvider = {
    provide: UpdateCaseCoincidenceUseCase,
    useFactory: updateCaseCoincidenceUseCaseFactory,
    deps: [CaseRepository]
}

export const deleteCoincidenceByIdUseCaseProvider = {
    provide: DeleteCoincidenceByIdUseCase,
    useFactory: deleteCaseCoincidenceByIdUseCaseFactory,
    deps: [CaseRepository]
}

export const getAllCoincidencesByCaseIdUseCaseProvider = {
    provide: GetAllCoincidencesByCaseIdUseCase,
    useFactory: getAllCoincidencesByCaseIdUseCaseFactory,
    deps: [CaseRepository]
}

export const getCoincidenceByIdUseCaseProvider = {
    provide: GetCoincidenceByIdUseCase,
    useFactory: getCoincidenceByIdUseCaseFactory,
    deps: [CaseRepository]
}

@NgModule({
    providers: [
        getAllCasesUseCaseProvider,
        getNumberOfCasesUseCaseProvider,
        addCaseUseCaseProvider,
        getCaseByIdUseCaseProvider,
        updateCaseUseCaseProvider,
        updateCaseLocationsUseCaseProvider,
        updateCaseGeneralInfoUseCaseProvider,
        updateCaseDetailsUseCaseProvider,
        updateRelatedCasesUseCaseProvider,
        updateRelatedSubjectsUseCaseProvider,
        addEvidenceCaseUseCaseProvider,
        updateCaseEvidenceUseCaseProvider,
        getEvidenceCaseByCaseIdUseCaseProvider,
        deleteEvidenceCaseByIdUseCaseProvider,
        deleteCaseByIdUseCaseProvider,
        deleteAllCaseLocationsByIdUseCaseProvider,
        deleteAllRelatedCasesByIdUseCaseProvider,
        deleteAllRelatedSubjectsByIdUseCaseProvider,
        addCommentCaseUseCaseProvider,
        getCommentCaseByCaseIdUseCaseProvider,
        addCoincidenceCaseUseCaseProvider,
        updateCaseCoincidenceUseCaseProvider,
        deleteCoincidenceByIdUseCaseProvider,
        getAllCoincidencesByCaseIdUseCaseProvider,
        getCoincidenceByIdUseCaseProvider,
        {provide: CaseRepository, useClass: CaseRepositoryImpl},
    ],
    imports: [
        CommonModule,
    ]
})
export class DiCriminalistictModule {
}