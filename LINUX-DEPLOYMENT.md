# Verazial Admin - Linux Deployment Guide

This guide covers deploying the Verazial Admin application on Linux servers.

## Quick Start

1. **Copy deployment package to your Linux server**
2. **Install dependencies** (if needed): `chmod +x install-dependencies.sh && ./install-dependencies.sh`
3. **Configure environment**: Edit `deployment-config.json`
4. **Deploy**: `chmod +x deploy.sh && ./deploy.sh`

## Prerequisites

- Linux server (Ubuntu 18.04+, CentOS 7+, RHEL 7+, or similar)
- Root or sudo access for initial setup
- Internet connection for downloading dependencies

## Step-by-Step Deployment

### 1. Prepare the Server

#### Option A: Automatic Installation
```bash
# Make the installation script executable
chmod +x install-dependencies.sh

# Run the installation script
./install-dependencies.sh

# Log out and log back in (or run: newgrp docker)
```

#### Option B: Manual Installation

**Install Docker:**
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# CentOS/RHEL
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

**Install jq:**
```bash
# Ubuntu/Debian
sudo apt-get update && sudo apt-get install -y jq

# CentOS/RHEL
sudo yum install -y jq
```

### 2. Configure the Application

Edit the `deployment-config.json` file:

```bash
nano deployment-config.json
```

Example configuration:
```json
{
  "grpcApiGateway": "https://prod-gatewaygrpc.verazial.com",
  "konektorAPI": "https://prod-konektor.verazial.com",
  "credentialsAPI": "https://prod-pass.verazial.com/api/v1/",
  "credential_user": "prod-user",
  "credential_password": "prod-password",
  "environment_name": "PRODUCTION",
  "port": "80"
}
```

### 3. Deploy the Application

```bash
# Make the deployment script executable
chmod +x deploy.sh

# Deploy with default configuration
./deploy.sh

# Or deploy with specific configuration file
./deploy.sh -c deployment-config.json

# Or deploy without loading images (if already loaded)
./deploy.sh --no-load-images
```

### 4. Verify Deployment

```bash
# Check container status
docker compose ps

# View logs
docker compose logs -f

# Test the application
curl http://localhost:PORT

# Check if port is listening
netstat -tuln | grep :PORT
```

## Deployment Script Options

The `deploy.sh` script supports several options:

```bash
./deploy.sh [OPTIONS]

Options:
  -c, --config FILE     Path to configuration file (default: deployment-config.json)
  -n, --no-load-images  Skip loading Docker images from .tar files
  -h, --help           Show help message

Examples:
  ./deploy.sh                                    # Deploy with default config
  ./deploy.sh -c deployment-config.prod.json    # Deploy with specific config
  ./deploy.sh --no-load-images                  # Deploy without loading images
```

## Configuration Management

### Environment-Specific Configurations

Create different configuration files for different environments:

```bash
# Development
cp deployment-config.json deployment-config.dev.json

# Staging
cp deployment-config.json deployment-config.staging.json

# Production
cp deployment-config.json deployment-config.prod.json
```

Deploy with specific environment:
```bash
./deploy.sh -c deployment-config.prod.json
```

### Updating Configuration

To update the configuration without rebuilding:

1. **Edit the configuration file:**
   ```bash
   nano deployment-config.json
   ```

2. **Restart the application:**
   ```bash
   docker compose restart
   ```

## Firewall Configuration

If you're using a firewall, make sure to open the required port:

```bash
# UFW (Ubuntu)
sudo ufw allow PORT/tcp

# firewalld (CentOS/RHEL)
sudo firewall-cmd --permanent --add-port=PORT/tcp
sudo firewall-cmd --reload

# iptables
sudo iptables -A INPUT -p tcp --dport PORT -j ACCEPT
```

## SSL/TLS Configuration

For production deployments, consider setting up SSL/TLS:

### Option 1: Reverse Proxy with Nginx

1. **Install Nginx:**
   ```bash
   sudo apt-get install nginx  # Ubuntu/Debian
   sudo yum install nginx      # CentOS/RHEL
   ```

2. **Configure Nginx:**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://localhost:PORT;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

3. **Install SSL certificate** (Let's Encrypt recommended)

### Option 2: Docker with SSL

Modify `docker-compose.yml` to include SSL certificates and expose port 443.

## Monitoring and Maintenance

### Useful Commands

```bash
# Check application status
docker compose ps

# View real-time logs
docker compose logs -f angular-app

# Check resource usage
docker stats

# Update application (with new image)
docker compose pull
docker compose up -d

# Backup configuration
cp deployment-config.json deployment-config.backup.$(date +%Y%m%d)

# Clean up old images
docker image prune -f
```

### Log Management

Application logs are available through Docker:

```bash
# View all logs
docker compose logs

# View logs for specific service
docker compose logs angular-app

# Follow logs in real-time
docker compose logs -f

# View last 100 lines
docker compose logs --tail=100
```

## Troubleshooting

### Common Issues

1. **Permission denied when running Docker commands:**
   ```bash
   # Add user to docker group
   sudo usermod -aG docker $USER
   # Log out and log back in
   ```

2. **Port already in use:**
   ```bash
   # Find what's using the port
   sudo netstat -tulpn | grep :PORT
   # Kill the process or change the port in config
   ```

3. **Container fails to start:**
   ```bash
   # Check logs
   docker compose logs
   # Check configuration file syntax
   jq . deployment-config.json
   ```

4. **Cannot connect to Docker daemon:**
   ```bash
   # Start Docker service
   sudo systemctl start docker
   # Enable Docker to start on boot
   sudo systemctl enable docker
   ```

5. **Configuration not applied:**
   ```bash
   # Check if config file is mounted correctly
   docker compose exec angular-app ls -la /app/config/
   # Check configuration script logs
   docker compose logs angular-app | grep configuration
   ```

### Getting Help

If you encounter issues:

1. Check the logs: `docker compose logs`
2. Verify configuration: `jq . deployment-config.json`
3. Check Docker status: `docker info`
4. Verify network connectivity to APIs
5. Check firewall settings

## Security Best Practices

1. **Use strong passwords** in configuration files
2. **Restrict file permissions** on configuration files:
   ```bash
   chmod 600 deployment-config.json
   ```
3. **Use HTTPS** for all API endpoints
4. **Keep Docker updated**
5. **Regularly update the application**
6. **Monitor logs** for suspicious activity
7. **Use a reverse proxy** with SSL termination
8. **Implement proper backup procedures**

## Backup and Recovery

### Backup

```bash
# Backup configuration
cp deployment-config.json backup/deployment-config-$(date +%Y%m%d).json

# Backup Docker volumes (if any)
docker compose down
tar -czf backup/volumes-$(date +%Y%m%d).tar.gz /var/lib/docker/volumes/
```

### Recovery

```bash
# Restore configuration
cp backup/deployment-config-YYYYMMDD.json deployment-config.json

# Redeploy
./deploy.sh
```

This guide should help you successfully deploy and manage the Verazial Admin application on Linux servers.
