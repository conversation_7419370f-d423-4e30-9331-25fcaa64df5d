import { NgModule } from '@angular/core';
import { UserNotVerifiedComponent } from './user-not-verified/user-not-verified.component';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MessagesModule } from 'primeng/messages';
import { WidgetMatchModule } from '../widget-match/widget-match.module';
import { DialogModule } from 'primeng/dialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { FloatLabelModule } from 'primeng/floatlabel';
import { ButtonModule } from 'primeng/button';
import { PasswordModule } from 'primeng/password';
import { ToastModule } from 'primeng/toast';
import { InactivityMonitorModule } from '../inactivity-monitor/inactivity-monitor.module';

@NgModule({
  declarations: [
    UserNotVerifiedComponent
  ],
  imports: [
    CommonModule,
    /* Translate */
    TranslateModule,
    /* Forms */
    FormsModule,
    ReactiveFormsModule,
    /* PrimeNG Modules */
    MessagesModule,
    DialogModule,
    PasswordModule,
    FloatLabelModule,
    ButtonModule,
    ToastModule,
    /* Custom Modules */
    WidgetMatchModule,
    InactivityMonitorModule,
  ],
  exports: [
    UserNotVerifiedComponent
  ]
})
export class UserNotVerifiedModule { }
