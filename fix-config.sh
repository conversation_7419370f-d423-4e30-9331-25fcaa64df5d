#!/bin/bash

# Quick fix script for configuration issues
# This script attempts to fix common configuration problems

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_color $GREEN "=== Verazial Admin Configuration Fix Script ==="

# Check if Docker Compose is available
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
elif docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    print_color $RED "Docker Compose not found!"
    exit 1
fi

# 1. Stop the container
print_color $YELLOW "1. Stopping containers..."
$DOCKER_COMPOSE down

# 2. Check if deployment-config.json exists
if [[ ! -f "deployment-config.json" ]]; then
    print_color $YELLOW "2. Creating default deployment-config.json..."
    cat > deployment-config.json << 'EOF'
{
  "grpcApiGateway": "https://dev-gatewaygrpc.verazial.com",
  "konektorAPI": "https://localhost:8443",
  "credentialsAPI": "https://dev-pass.verazial.com/api/v1/",
  "credential_user": "verazial",
  "credential_password": "@4S9w&JMhg27",
  "environment_name": "DEV",
  "port": "20002"
}
EOF
    print_color $GREEN "✓ Created default deployment-config.json"
else
    print_color $GREEN "✓ deployment-config.json already exists"
fi

# 3. Validate JSON
print_color $YELLOW "3. Validating configuration file..."
if jq empty deployment-config.json 2>/dev/null; then
    print_color $GREEN "✓ Configuration file is valid JSON"
else
    print_color $RED "✗ Configuration file is invalid JSON. Please fix it manually."
    exit 1
fi

# 4. Check docker-compose.yml volume mounting
print_color $YELLOW "4. Checking docker-compose.yml..."
if ! grep -q "deployment-config.json:/app/config/deployment-config.json" docker-compose.yml; then
    print_color $YELLOW "Adding volume mount to docker-compose.yml..."
    
    # Create a backup
    cp docker-compose.yml docker-compose.yml.backup
    
    # Add volume mount if not present
    if grep -q "volumes:" docker-compose.yml; then
        # Volumes section exists, add our mount
        sed -i '/volumes:/a\      - ./deployment-config.json:/app/config/deployment-config.json:ro' docker-compose.yml
    else
        # No volumes section, add it
        sed -i '/restart: unless-stopped/a\    volumes:\n      - ./deployment-config.json:/app/config/deployment-config.json:ro' docker-compose.yml
    fi
    
    print_color $GREEN "✓ Added volume mount to docker-compose.yml"
else
    print_color $GREEN "✓ Volume mount already configured"
fi

# 5. Set PORT environment variable
PORT=$(jq -r '.port' deployment-config.json)
export PORT="$PORT"
print_color $GREEN "✓ Set PORT environment variable to: $PORT"

# 6. Start the container
print_color $YELLOW "6. Starting containers..."
$DOCKER_COMPOSE up -d

# 7. Wait for container to start
print_color $YELLOW "7. Waiting for container to start..."
sleep 5

# 8. Check if container is running
CONTAINER_NAME=$($DOCKER_COMPOSE ps -q angular-app 2>/dev/null)
if [[ -n "$CONTAINER_NAME" ]]; then
    print_color $GREEN "✓ Container is running"
    
    # 9. Check if configuration script ran
    print_color $YELLOW "9. Checking configuration script execution..."
    sleep 3
    
    if $DOCKER_COMPOSE logs angular-app | grep -q "Environment configuration completed"; then
        print_color $GREEN "✓ Configuration script executed successfully"
    else
        print_color $YELLOW "Configuration script may not have run. Checking logs..."
        $DOCKER_COMPOSE logs angular-app | tail -20
        
        # Try to run the configuration script manually
        print_color $YELLOW "Attempting to run configuration script manually..."
        if docker exec "$CONTAINER_NAME" /usr/local/bin/configure-environment.sh; then
            print_color $GREEN "✓ Configuration script executed manually"
        else
            print_color $RED "✗ Configuration script failed"
        fi
    fi
    
    # 10. Verify configuration was applied
    print_color $YELLOW "10. Verifying configuration was applied..."
    
    # Check if placeholders are still present
    if docker exec "$CONTAINER_NAME" find /usr/share/nginx/html -name "*.js" -exec grep -l "__GRPC_API_GATEWAY__" {} \; | head -1 | grep -q .; then
        print_color $RED "✗ Placeholders still found in JavaScript files"
        print_color $YELLOW "Attempting to fix by restarting container..."
        $DOCKER_COMPOSE restart
        sleep 5
        
        # Check again
        if docker exec "$CONTAINER_NAME" find /usr/share/nginx/html -name "*.js" -exec grep -l "__GRPC_API_GATEWAY__" {} \; | head -1 | grep -q .; then
            print_color $RED "✗ Configuration still not applied. Manual intervention required."
        else
            print_color $GREEN "✓ Configuration applied after restart"
        fi
    else
        print_color $GREEN "✓ Configuration has been applied successfully"
    fi
    
else
    print_color $RED "✗ Container failed to start"
    print_color $YELLOW "Container logs:"
    $DOCKER_COMPOSE logs
    exit 1
fi

echo ""
print_color $GREEN "=== Fix Script Completed ==="
print_color $CYAN "Application should now be running with your configuration"
print_color $CYAN "Access URL: http://localhost:$PORT"

echo ""
print_color $YELLOW "To verify everything is working:"
print_color $WHITE "1. Check status: $DOCKER_COMPOSE ps"
print_color $WHITE "2. View logs: $DOCKER_COMPOSE logs -f"
print_color $WHITE "3. Test application: curl http://localhost:$PORT"

echo ""
print_color $YELLOW "If you still have issues:"
print_color $WHITE "1. Run: ./diagnose-config.sh"
print_color $WHITE "2. Check the application in your browser"
print_color $WHITE "3. Look for any JavaScript console errors"
