import {
    SegmentedSearchFilter
} from "src/verazial-common-frontend/core/general/manager/common/models/segmented-search-filter.model";

export class ComparatorResultModelEntity {
    subject: {
        nId: string,
        subjectId: string,
        biographicId: string
    } = {
        nId: '',
        subjectId: '',
        biographicId: ''
    };
    image: string = "";
    score: number = -1;
    searchFilters: SegmentedSearchFilter[] = [];
}