/** Audit Trail Actions **/
export enum AuditTrailActions {

    /**
     * General (All)
     */
    /* Auth MS */
    LOGIN = 'LOGIN', // Login
    LOGIN_BIO = 'LOGIN_BIO', // Biometric Login
    CHECK_TOKEN = 'CHECK_TOKEN', // Check Token
    AUTHENTICATE_BY_LICENSE = 'AUTHENTICATE_BY_LICENSE', // Authenticate By License
    GET_LICENSE_REPORT = 'GET_LICENSE_REPORT', // Get License Report
    /* Widget Verify & Search */
    USER_VERIFY = 'USER_VERIFY', // User Verify
    SUBJECT_VERIFY = 'SUBJECT_VERIFY', // Subject Verify
    USER_SEARCH = 'USER_SEARCH', // User Search
    SUBJECT_SEARCH = 'SUBJECT_SEARCH', // Subject Search
    /* Manager MS */
    GET_SETTINGS_BY_APPLICATION = 'GET_SETTINGS_BY_APPLICATION', // Get Settings By Application
    /* Monitoring MS */
    GET_SYSTEM_REPORT = 'GET_SYSTEM_REPORT', // Get System Report
    GET_ACTUAL_TIME = 'GET_ACTUAL_TIME', // Get Actual Time
    GET_BIOMETRIC_SAMPLE_COUNT = 'GET_BIOMETRIC_SAMPLE_COUNT', // Get Biometric Sample Count
    GET_SERVER_MS_STATUS = 'GET_SERVER_MS_STATUS', // Get Server MS Status
    /* User MS */
    GET_NUMBER_OF_USERS = 'GET_NUMBER_OF_USERS', // Get Number Of Users
    GET_USER_BY_NUM_ID = 'GET_USER_BY_NUM_ID', // Get User By Num Id
    MOD_PASS = 'MOD_PASS', // Modify User Password
    PW_RECOVERY_REQUEST = 'PW_RECOVERY_REQUEST', // Password Recovery Request
    PW_RECOVERY = 'PW_RECOVERY', // Password Recovery
    // Roles
    GET_ALL_ROLES_BY_USER_ID = 'GET_ALL_ROLES_BY_USER_ID', // Get All Roles By User Id
    /* Subject MS */
    GET_NUMBER_OF_SUBJECTS = 'GET_NUMBER_OF_SUBJECTS', // Get Number Of Subjects
    /* Konektor Service */
    GET_KONEKTOR_PROPERTIES = 'GET_KONEKTOR_PROPERTIES', // Get Konektor Properties
    /* Actions (V2) MS */
    SEARCH_ACTIONS = 'SEARCH_ACTIONS', // Search Actions

    /**
     * Admin
     */
    /* User MS */
    GET_ALL_USERS = 'GET_ALL_USERS', // Get All Users
    ADD_USR = 'ADD_USR', // Add User
    MOD_USR = 'MOD_USR', // Modify User
    DEL_USR = 'DEL_USR', // Delete User
    // Details
    GET_USER_DETAILS_BY_USER_ID = 'GET_USER_DETAILS_BY_USER_ID', // Get User Details By User Id
    SAVE_USER_DETAIL = 'SAVE_USER_DETAIL', // Save User Detail
    UPDATE_USER_DETAILS = 'UPDATE_USER_DETAIL', // Update User Details
    // Roles
    ADD_USER_ROLES = 'ADD_USER_ROLES', // Add User Roles
    DELETE_USER_ROLES = 'DELETE_USER_ROLES', // Delete User Roles
    /* Role MS */
    GET_ALL_ROLES = 'GET_ALL_ROLES', // Get All Roles
    GET_ALL_ACCESSES = 'GET_ALL_ACCESSES', // Get All Accesses
    GET_ROLE_BY_ID = 'GET_ROLE_BY_ID', // Get Role By Id
    ADD_ROL = 'ADD_ROL', // Add Role
    MOD_ROL = 'MOD_ROL', // Modify Role
    DEL_ROL = 'DEL_ROL', // Delete Role
    ADD_ROL_ACC = 'ADD_ROL_ACC', // Add Role Access
    DEL_ROL_ACC = 'DEL_ROL_ACC', // Delete Role Access
    /* Subject MS */
    GET_ALL_SUBJECTS = 'GET_ALL_SUBJECTS', // Get All Subjects
    GET_SUBJECT_BY_NUM_ID = 'GET_SUBJECT_BY_NUM_ID', // Get Subject By Num Id
    GET_SUBJECT_BY_ID = 'GET_SUBJECT_BY_ID', // Get Subject By Id
    ADD_SUB = 'ADD_SUB', // Add Subject
    MOD_SUB = 'MOD_SUB', // Modify Subject
    DEL_SUB = 'DEL_SUB', // Delete Subject
    /* Criminalistics MS */
    GET_ALL_CASES = 'GET_ALL_CASES', // Get All Cases
    GET_NUMBER_OF_CASES = 'GET_NUMBER_OF_CASES', // Get number of Cases
    GET_CASE_BY_ID = 'GET_CASE_BY_ID', // Get Tenant By Id
    ADD_CASE = 'ADD_CASE', // Add Case
    MOD_CASE = 'MOD_CASE', // Modify Case
    MOD_GEN_INF_CASE = 'MOD_GENERAL_INFO_CASE', // Modify General Info Case
    MOD_LOC_CASE = 'MOD_LOCATION_CASE', // Modify Location Case
    MOD_DET_CASE = 'MOD_DETAIL_CASE', // Modify Detail Case
    MOD_REL_SUB_CASE = 'MOD_RELATED_SUBJECT_CASE', // Modify Related Subject Case
    MOD_REL_CASE = 'MOD_RELATIONS_CASE', // Modify Relations Case
    MOD_REL_CAS_CASE = 'MOD_RELATED_CASES_CASE', // Modify Related Cases
    DEL_CASE = 'DEL_CASE', // Delete Case
    DEL_LOC_CASE = 'DEL_LOCATION_CASE', // Delete Location Case
    DEL_REL_SUB_CASE = 'DEL_RELATED_SUBJECT_CASE', // Delete Related Subject Case
    ADD_EVIDENCE_CASE = 'ADD_EVIDENCE_CASE', // Add Evidence Case
    MOD_EVIDENCE_CASE = 'MOD_EVIDENCE_CASE', // Modify Evidence Case
    GET_EVIDENCE_BY_CASE_ID = 'GET_EVIDENCE_BY_CASE_ID', // Get Evidence Case by id
    DEL_EVIDENCE_BY_CASE_ID = 'DEL_EVIDENCE_BY_CASE_ID', // Del Evidence Case by id
    ADD_COINCIDENCE_CASE = 'ADD_COINCIDENCE_CASE', // Add coincidence
    MOD_COINCIDENCE_CASE = 'MOD_COINCIDENCE_CASE', // Modify coincidence
    GET_COINCIDENCE_BY_CASE_ID = 'GET_COINCIDENCE_BY_CASE_ID', // Get coincidence Case by id
    GET_COINCIDENCE_BY_ID = 'GET_COINCIDENCE_BY_ID', // Get coincidence Case by id
    DEL_COINCIDENCE_BY_CASE_ID = 'DEL_COINCIDENCE_BY_CASE_ID', // Del coincidence Case by id
    /* API Binder */
    UNENROLL_IDENTITY = 'UNENROLL_IDENTITY', // Unenroll Identity
    // Details
    GET_SUBJECT_DETAILS_BY_SUBJECT_ID = 'GET_SUBJECT_DETAILS_BY_SUBJECT_ID', // Get Subject Details By Subject Id
    SAVE_SUBJECT_DETAIL = 'SAVE_SUBJECT_DETAIL', // Save Subject Detail
    UPDATE_SUBJECT_DETAILS = 'UPDATE_SUBJECT_DETAIL', // Update Subject Details
    // Roles
    GET_ALL_ROLES_BY_SUBJECT_ID = 'GET_ALL_ROLES_BY_SUBJECT_ID', // Get All Roles By Subject Id
    ADD_SUBJECT_ROLES = 'ADD_SUBJECT_ROLES', // Add Subject Roles
    DELETE_SUBJECT_ROLES = 'DELETE_SUBJECT_ROLES', // Delete Subject Roles
    /* Related Subject MS */
    SAVE_RELATED_SUBJECT = 'SAVE_RELATED_SUBJECT', // Save Related Subject
    GET_RELATED_SUBJECT_BY_ID = 'GET_RELATED_SUBJECT_BY_ID', // Get Related Subject By Id
    GET_RELATED_SUBJECTS_BY_SUBJECT_ID = 'GET_RELATED_SUBJECTS_BY_SUBJECT_ID', // Get Related Subjects By Subject Id
    GET_RELATED_SUBJECTS_BY_RELATED_SUBJECT_ID = 'GET_RELATED_SUBJECTS_BY_RELATED_SUBJECT_ID', // Get Related Subjects By Related Subject Id
    SEARCH_RELATED_SUBJECTS = 'SEARCH_RELATED_SUBJECTS', // Search Related Subjects
    UPDATE_RELATED_SUBJECT = 'UPDATE_RELATED_SUBJECT', // Update Related Subject
    DELETE_RELATED_SUBJECT = 'DELETE_RELATED_SUBJECT', // Delete Related Subject
    /* Subject Locations MS */
    SAVE_USER_LOCATION = 'SAVE_USER_LOCATION', // Save User Location
    GET_SUBJECT_LOCATION_BY_SUBJECT_ID = 'GET_SUBJECT_LOCATION_BY_SUBJECT_ID', // Get Subject Location By Subject Id
    UPDATE_USER_LOCATION = 'UPDATE_USER_LOCATION', // Update User Location
    DELETE_USER_LOCATION = 'DELETE_USER_LOCATION', // Delete User Location
    /* Belongings Prisons MS */
    GET_ALL_BELONGINGS = 'GET_ALL_BELONGINGS', // Get All Belongings
    GET_BELONGING_BY_ID = 'GET_BELONGING_BY_ID', // Get Belonging By Id
    GET_BELONGINGS_BY_SUBJECT_ID = 'GET_BELONGINGS_BY_SUBJECT_ID', // Get Belongings By Subject Id
    ADD_BLG = 'ADD_BLG', // Add Belonging
    MOD_BLG = 'MOD_BLG', // Modify Belonging
    DEL_BLG = 'DEL_BLG', // Delete Belonging
    /* Judicial Files Prisons MS */
    GET_ALL_JUDICIAL_FILES = 'GET_ALL_JUDICIAL_FILES', // Get All Judicial Files
    GET_JUDICIAL_FILE_BY_ID = 'GET_JUDICIAL_FILE_BY_ID', // Get Judicial File By Id
    GET_JUDICIAL_FILES_BY_SUBJECT_ID = 'GET_JUDICIAL_FILES_BY_SUBJECT_ID', // Get Judicial Files By Subject Id
    ADD_JDF = 'ADD_JDF', // Add Judicial File
    MOD_JDF = 'MOD_JDF', // Modify Judicial File
    DEL_JDF = 'DEL_JDF', // Delete Judicial File
    /* Konektor Service */
    KONEKTOR_TAKE_PHOTO = 'KONEKTOR_TAKE_PHOTO', // Konektor Take Photo
    /* Storage MS */
    GET_STATIC_RESOURCE_BY_SUBJECT_ID = 'GET_STATIC_RESOURCE_BY_SUBJECT_ID', // Get Static Resource By Subject Id
    GET_STATIC_RESOURCE_BY_SUBJECT_ID_AND_NAME = 'GET_STATIC_RESOURCE_BY_SUBJECT_ID_AND_NAME', // Get Static Resource By Subject Id And Name
    GET_STATIC_RESOURCE_BY_SUBJECT_ID_AND_NAME_AND_NUMBER = 'GET_STATIC_RESOURCE_BY_SUBJECT_ID_AND_NAME_AND_NUMBER', // Get Static Resource By Subject Id And Name And Number
    ADD_STA_RES = 'ADD_STA_RES', // Add Static Resource
    DEL_STA_RES = 'DEL_STA_RES', // Delete Static Resource
    /* Tenant MS */
    GET_ALL_TENANTS = 'GET_ALL_TENANTS', // Get All Tenants
    GET_TENANT_BY_ID = 'GET_TENANT_BY_ID', // Get Tenant By Id
    ADD_TNT = 'ADD_TNT', // Add Tenant
    MOD_TNT = 'MOD_TNT', // Modify Tenant
    DEL_TNT = 'DEL_TNT', // Delete Tenant
    /* Admin MS */
    // Flows
    ADD_FLW = 'ADD_FLW', // Add Flow
    MOD_FLW = 'MOD_FLW', // Modify Flow
    DEL_FLW = 'DEL_FLW', // Delete Flow
    // Task Flows
    GET_ALL_TASK_FLOWS = 'GET_ALL_TASK_FLOWS', // Get All Task Flows
    GET_TASK_FLOW_BY_ID = 'GET_TASK_FLOW_BY_ID', // Get Task Flow By Id
    GET_ALL_PUBLISHED_TASK_FLOWS = 'GET_ALL_PUBLISHED_TASK_FLOWS', // Get All Published Task Flows
    // Draw Flows
    GET_DRAW_FLOW_BY_TASK_FLOW_ID = 'GET_DRAW_FLOW_BY_TASK_FLOW_ID', // Get Draw Flow By Task Flow Id
    // Categories
    GET_ALL_GROUP_CATEGORIES = 'GET_ALL_GROUP_CATEGORIES', // Get All Group Categories
    GET_GROUP_CATEGORY_BY_ID = 'GET_GROUP_CATEGORY_BY_ID', // Get Group Category By Id
    ADD_CAT = 'ADD_CAT', // Add Category
    MOD_CAT = 'MOD_CAT', // Modify Category
    DEL_CAT = 'DEL_CAT', // Delete Category
    // Assignments
    GET_ALL_ASSIGNMENTS = 'GET_ALL_ASSIGNMENTS', // Get All Assignments
    ADD_ASG = 'ADD_ASG', // Add Assignment
    MOD_ASG = 'MOD_ASG', // Modify Assignment
    DEL_ASG = 'DEL_ASG', // Delete Assignment
    DEL_ASG_ELM = 'DEL_ASG_ELM', // Delete Assignment Element
    // Prisons Add-On: Authorization Schedules
    GET_ALL_AUTH_SCHEDULES = 'GET_ALL_AUTH_SCHEDULES', // Get All Authorization Schedules
    GET_AUTH_SCHEDULE_BY_ID = 'GET_AUTH_SCHEDULE_BY_ID', // Get Authorization Schedule
    ADD_AUTH_SCHEDULE = 'ADD_AUTH_SCHEDULE', // Add Authorization Schedule
    MOD_AUTH_SCHEDULE = 'MOD_AUTH_SCHEDULE', // Modify Authorization Schedule
    DEL_AUTH_SCHEDULE = 'DEL_AUTH_SCHEDULE', // Delete Authorization Schedule
    // Prisons Add-On: Transfer Authorizations
    GET_ALL_TRANSFER_AUTHORIZATIONS = 'GET_ALL_TRANSFER_AUTHORIZATIONS', // Get All Transfer Authorizations
    GET_TRANSFER_AUTHORIZATION_BY_ID = 'GET_TRANSFER_AUTHORIZATION_BY_ID', // Get Transfer Authorization
    ADD_TRANSFER_AUTHORIZATION = 'ADD_TRANSFER_AUTHORIZATION', // Add Transfer Authorization
    MOD_TRANSFER_AUTHORIZATION = 'MOD_TRANSFER_AUTHORIZATION', // Modify Transfer Authorization
    DEL_TRANSFER_AUTHORIZATION = 'DEL_TRANSFER_AUTHORIZATION', // Delete Transfer Authorization
    // Prisons Add-On: Entry Exit Authorizations
    GET_ALL_ENTRY_EXIT_AUTHORIZATIONS = 'GET_ALL_ENTRY_EXIT_AUTHORIZATIONS', // Get All Entry Exit Authorizations
    GET_ENTRY_EXIT_AUTHORIZATION_BY_ID = 'GET_ENTRY_EXIT_AUTHORIZATION_BY_ID', // Get Entry Exit Authorization
    GET_ENTRY_EXIT_AUTHORIZATION_BY_SUBJECT_ID = 'GET_ENTRY_EXIT_AUTHORIZATION_BY_SUBJECT_ID', // Get Entry Exit Authorization By Subject Id
    ADD_ENTRY_EXIT_AUTHORIZATION = 'ADD_ENTRY_EXIT_AUTHORIZATION', // Add Entry Exit Authorization
    MOD_ENTRY_EXIT_AUTHORIZATION = 'MOD_ENTRY_EXIT_AUTHORIZATION', // Modify Entry Exit Authorization
    DEL_ENTRY_EXIT_AUTHORIZATION = 'DEL_ENTRY_EXIT_AUTHORIZATION', // Delete Entry Exit Authorization
    /* Pass MS */
    // Data Sources
    GET_ALL_DATA_SOURCES = 'GET_ALL_DATA_SOURCES', // Get All Data Sources
    ADD_DS = 'ADD_DS', // Add Data Source
    MOD_DS = 'MOD_DS', // Modify Data Source
    DEL_DS = 'DEL_DS', // Delete Data Source
    // Data Source Params
    GET_ALL_PARAMS_BY_DATA_SOURCE_ID = 'GET_ALL_PARAMS_BY_DATA_SOURCE_ID', // Get All Params By Data Source Id
    GET_APP_DATA_SOURCE_PARAM_BY_ID = 'GET_APP_DATA_SOURCE_PARAM_BY_ID', // Get App Data Source Param By Id
    ADD_DS_PARAM = 'ADD_DS_PARAM', // Add Data Source Param
    MOD_DS_PARAM = 'MOD_DS_PARAM', // Modify Data Source Param
    DEL_DS_PARAM = 'DEL_DS_PARAM', // Delete Data Source Param
    // Applications
    GET_ALL_APPLICATIONS = 'GET_ALL_APPLICATIONS', // Get All Applications
    ADD_APP = 'ADD_APP', // Add Application
    MOD_APP = 'MOD_APP', // Modify Application
    DEL_APP = 'DEL_APP', // Delete Application
    // Application Flows
    ADD_APP_FLW = 'ADD_APP_FLW', // Add Application Flow
    MOD_APP_FLW = 'MOD_APP_FLW', // Modify Application Flow
    DEL_APP_FLW = 'DEL_APP_FLW', // Delete Application Flow
    // Application Windows
    ADD_APP_WIN = 'ADD_APP_WIN', // Add Application Window
    MOD_APP_WIN = 'MOD_APP_WIN', // Modify Application Window
    DEL_APP_WIN = 'DEL_APP_WIN', // Delete Application Window
    // Application Windows Params
    ADD_APP_WIN_PARAM = 'ADD_APP_WIN_PARAM', // Add Application Window Param
    MOD_APP_WIN_PARAM = 'MOD_APP_WIN_PARAM', // Modify Application Window Param
    DEL_APP_WIN_PARAM = 'DEL_APP_WIN_PARAM', // Delete Application Window Param
    // Subject Apps
    ADD_PSS = 'ADD_PSS', // Add Subject App
    MOD_PSS = 'MOD_PSS', // Modify Subject App
    DEL_PSS = 'DEL_PSS', // Delete Subject App
    // Subject App Credentials
    ADD_PSS_CRED = 'ADD_PSS_CRED', // Add Subject App Credential
    MOD_PSS_CRED = 'MOD_PSS_CRED', // Modify Subject App Credential
    DEL_PSS_CRED = 'DEL_PSS_CRED', // Delete Subject App Credential
    // Application Credentials
    ADD_APP_CRED = 'ADD_APP_CRED', // Add Application Credential
    MOD_APP_CRED = 'MOD_APP_CRED', // Modify Application Credential
    DEL_APP_CRED = 'DEL_APP_CRED', // Delete Application Credential
    // Application Registry
    GET_ALL_APP_REGISTRIES = 'GET_ALL_APP_REGISTRIES', // Get All Application Registries
    ADD_APP_REG = 'ADD_APP_REG', // Add Application Registry
    MOD_APP_REG = 'MOD_APP_REG', // Modify Application Registry
    DEL_APP_REG = 'DEL_APP_REG', // Delete Application Registry
    /* Reports */
    EXPORT_REPORT = 'EXP_REP', // Export Report Audit Trail

    /**
     * Clock
     */
    ABORT = 'ABORT', // Abort
    MOD_LCF = 'MOD_LCF', // Modify Local Configuration
    SET_AVAILABLE_ACTIONS = 'SET_AVAILABLE_ACTIONS', // Set Available Actions
    ACTION_FILL_REQUIRED_FORM = 'ACTION_FILL_REQUIRED_FORM', // Action Fill Required Form
    SELECT_ACTION_TO_PERFORM = 'SELECT_ACTION_TO_PERFORM', // Select Action To Perform
    EXECUTE_ACTION = 'EXECUTE_ACTION', // Execute Action
    /* Konektor Service */
    FIRE_KONEKTOR_RELAY = 'FIRE_KONEKTOR_RELAY', // Fire Konektor Relay
    /* Admin MS */
    SEARCH_ASSIGNMENT_BY = 'SEARCH_ASSIGNMENT_BY', // Search Assignment By

    /**
     * Manager
     */
    // Locations
    ADD_LOC = 'ADD_LOC', // Add Location
    REM_LOC = 'REM_LOC', // Remove Location
    // Segments
    ADD_SEG = 'ADD_SEG', // Add Segment
    REM_SEG = 'REM_SEG', // Remove Segment
    // Devices
    ADD_DEV = 'ADD_DEV', // Add Device
    REM_DEV = 'REM_DEV', // Remove Device
    // Licenses
    ADD_LIC = 'ADD_LIC', // Add License
    MOD_LIC = 'MOD_LIC', // Modify License
    REM_LIC = 'REM_LIC', // Remove License
    // Technologies
    MOD_TECH = 'MOD_TECH', // Modify Technology

    /**
     * Pass
     */
    /* Pass MS */
    PASS_PROCESS = 'PASS_PROCESS', // Pass Process

    /**
     * Widget
     */
    MCH_IDN = 'MCH_IDN', // Matching Identification
    MCH_VRF = 'MCH_VRF', // Matching Verification
    ID_PIN = 'ID_PIN', // Identification by nId
    NO_DEV = 'NO_DEV', // No Device
    NEW_SUB = 'NEW_SUB', // New Subject
    REM_SUB_BT = 'REM_SUB_BT', // Remove Subject By Button
    REM_SUB_SAM = 'REM_SUB_SAM', // Remove Subject By Sample
    NEW_SAM = 'NEW_SAM', // New Sample
    MOD_SAM = 'MOD_SAM', // Modify Sample
    REM_SAM = 'REM_SAM', // Remove Sample
}