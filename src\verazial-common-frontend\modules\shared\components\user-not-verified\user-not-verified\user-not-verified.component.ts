import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { WidgetResult } from "src/verazial-common-frontend/modules/shared/models/widget-response.model";
import { CheckTokenUseCase } from "src/verazial-common-frontend/core/general/auth/domain/use-cases/check-token.use-case";
import { KonektorPropertiesEntity } from "src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity";
import { GetKonektorPropertiesUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { RefreshByUserUseCase } from "src/verazial-common-frontend/core/general/auth/domain/use-cases/refresh-by-user.use-case";
import { DisablePrivateKeyUseCase } from "src/verazial-common-frontend/core/general/manager/domain/use-cases/disable-private-key.use-case";
import { environment } from "src/environments/environment";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { AuditTrailService } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { ValidatorService } from "../../../services/validator.service";
import { MessageService } from "primeng/api";
import { TranslateService } from "@ngx-translate/core";
import { CheckPermissionsService, ValidTenantResult } from "src/verazial-common-frontend/core/services/check-permissions-service";

@Component({
  selector: 'app-user-not-verified',
  templateUrl: './user-not-verified.component.html',
  styleUrl: './user-not-verified.component.css'
})
export class UserNotVerifiedComponent implements OnInit {

  /* User */
  userNumId: string = this.localStorageService.getUser().numId;
  user?: SubjectEntity;
  /* Widget */
  widgetUrl: string = "";
  userVerifyReady: boolean = false;
  /* Private Key Form */
  showPrivateKeyFormDialog: boolean = false;
  form: FormGroup = new FormGroup({
    privateKey: new FormControl('', Validators.required)
  });
  invalidKey: boolean = false;
  /* Settings */
  managerSettings?: GeneralSettings;
  konektorProperties?: KonektorPropertiesEntity;
  /* Page State */
  loading: boolean = false;
  confirmDialogTimeoutLimit: number = 0;
  startCheckingInactivity: boolean = false;

  disabledToolTip: string = "";

  /* Inputs */
  @Input() subjectType: string = "";
  @Input() verifyReady: boolean = false;
  @Input() userIsVerified: boolean = false;
  /* Outputs */
  @Output() modified = new EventEmitter<boolean>();

  constructor(
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    private checkTokenUseCase: CheckTokenUseCase,
    private refreshByUserUseCase: RefreshByUserUseCase,
    private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
    private disablePrivateKeyUseCase: DisablePrivateKeyUseCase,
    private auditTrailService: AuditTrailService,
    private messageService: MessageService,
    private translateService: TranslateService,
    private checkPermissionsService: CheckPermissionsService,
  ) { }

  async ngOnInit() {
    this.loading = true;
    this.userNumId = this.localStorageService.getUser().numId;
    this.user = this.localStorageService.getUser();
    // if (this.localStorageService.isUserVerified()) {
    //   await this.checkTokenUseCase.execute({ token: this.localStorageService.getItem('bio_auth')! }).then(
    //     (response) => {
    //       this.userIsVerified = !response;
    //     },
    //     (e) => {
    //       this.userIsVerified = false;
    //     }
    //   );
    // }
    // else {
    //   this.userIsVerified = false;
    // }
    this.managerSettings = this.localStorageService.getSessionSettings()!;
    this.widgetUrl = this.managerSettings?.widgetConfig?.url || "";
    this.getKonektorPropertiesUseCase.execute().subscribe({
      next: (data) => {
        if (data) {
          if (data.apiGatewayGrpc) {
            this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
          }
          else {
            this.localStorageService.destroyApiGatewayURL();
          }
          this.konektorProperties = data;
        }
      },
      error: (e) => {
        this.userIsVerified = false;
      },
      complete: () => {
        this.loading = false;
      }
    });
  }

  onUserWidgetMatchResult(event: WidgetResult) {
    if (!this.userVerifyReady) {
      return;
    }
    switch (event.action) {
      case "verify":
        if (event.result == "success") {
          if (event.data.isMatched) {
            this.localStorageService.setUserVerified(event.data.token);
            const at_attributes = [
              { name: AuditTrailFields.REGISTRATION_CODE, value: 'VER_BIO' },
            ]
            this.auditTrailService.registerAuditTrailAction(this.userNumId, AuditTrailActions.LOGIN_BIO, 0, 'SUCCESS', event.data.tech, at_attributes);
          }
        }
        this.updateModified(false);
        this.userVerifyReady = false;
        break;
      case "process":
        this.updateModified(event.result.toLowerCase().includes("started"))
        break;
      case "close_verify":
      case "error":
        if (event.result == "error_user_without_samples") {
          this.managerSettings = this.localStorageService.getSessionSettings()!;
          if (this.managerSettings?.privateKeyAv == true) {
            this.localStorageService.setItem('keyAt', '0');
            this.showPrivateKeyFormDialog = true;
            this.setPrivateKeyFormDialogTimeout();
            break;
          }
        }
        this.localStorageService.setUserVerified("");
        this.updateModified(false);
        this.userVerifyReady = false;
        break;
    }
    this.checkUserVerification();
  }

  async checkUserVerification() {
    if (this.localStorageService.isUserVerified()) {
      await this.checkTokenUseCase.execute({ token: this.localStorageService.getItem('bio_auth')! }).then(
        (response) => {
          this.userIsVerified = !response;
        },
        (e) => {
          this.userIsVerified = false;
        }
      );
    }
    else {
      this.userIsVerified = false;
    }
  }

  verifyUser() {
    this.updateModified(true);
    this.userNumId = this.localStorageService.getUser().numId;
    this.userVerifyReady = true;
  }

  updateModified(mod: boolean) {
    this.modified.emit(mod);
  }

  isValid(field: string) {
    var validatorService = new ValidatorService();
    return validatorService.isValidField(this.form, field);
  }

  setPrivateKeyFormDialogTimeout() {
    this.invalidKey = false;
    this.resetInactivityMonitor();
    // Set a timeout to automatically trigger the reject action after 10 seconds
    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  resetInactivityMonitor() {
    this.startCheckingInactivity = false;
    this.confirmDialogTimeoutLimit = 0;
  }

  onDialogSubmit() {
    this.resetInactivityMonitor();
    this.form.markAllAsTouched();
    const accessKey = this.form.get('privateKey')?.value;
    if (this.form.valid && accessKey == this.localStorageService.getSessionSettings()?.privateKey) {
      this.disablePrivateKeyUseCase.execute({ applicationName: environment.application.toUpperCase() }).then(
        (data) => {
          if (data && data.settings) {
            this.localStorageService.setSessionSettings(data.settings);
            this.refreshByUserUseCase.execute({ oldToken: this.localStorageService.getToken()! }).then(
              (data) => {
                if (data && data.token) {
                  this.localStorageService.setUserVerified(data.token);
                  const at_attributes = [
                    { name: AuditTrailFields.REGISTRATION_CODE, value: 'OTP' },
                  ]
                  this.auditTrailService.registerAuditTrailAction(this.userNumId, AuditTrailActions.LOGIN_BIO, 0, 'SUCCESS', '', at_attributes);
                }
              },
              (e) => {
                this.loggerService.error(e);
                const at_attributes = [
                  { name: AuditTrailFields.REGISTRATION_CODE, value: 'OTP' },
                  { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                ]
                this.auditTrailService.registerAuditTrailAction(this.userNumId, AuditTrailActions.LOGIN_BIO, 0, 'ERROR', '', at_attributes);
              }
            )
              .finally(() => {
                this.form.reset();
                this.updateModified(false);
                this.showPrivateKeyFormDialog = false;
                this.userVerifyReady = false;
                this.checkUserVerification();
              });
          }
        },
        (e) => {
          this.loggerService.error(e);
          this.form.reset();
          this.updateModified(false);
          this.showPrivateKeyFormDialog = false;
          this.userVerifyReady = false;
          this.checkUserVerification();
        }
      )
    }
    else {
      this.invalidKey = true;
      const at_attributes = [
        { name: AuditTrailFields.REGISTRATION_CODE, value: 'OTP' },
        { name: AuditTrailFields.ERROR_DESCRIPTION, value: 'Invalid access access key' },
      ]
      this.auditTrailService.registerAuditTrailAction(this.userNumId, AuditTrailActions.LOGIN_BIO, 0, 'ERROR', '', at_attributes);
      let attempts = 0
      if (this.localStorageService.getItem('keyAt')) {
        const done = this.localStorageService.getItem('keyAt');
        attempts = parseInt(done!) + 1;
      }
      else {
        attempts = 1;
      }
      this.localStorageService.setItem('keyAt', attempts.toString());
      if (attempts == 2) {
        this.messageService.add({
          severity: 'warn',
          summary: this.translateService.instant('messages.warning'),
          detail: this.translateService.instant('messages.disableOnNextAttempt')
        })
      }
      if (attempts >= 3) {
        this.disablePrivateKeyUseCase.execute({ applicationName: environment.application.toUpperCase() }).then(
          (data) => {
            if (data && data.settings) {
              this.localStorageService.setSessionSettings(data.settings);
              this.form.reset();
              this.updateModified(false);
              this.showPrivateKeyFormDialog = false;
              this.userVerifyReady = false;
              this.checkUserVerification();
              this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('headers.unauthorized'),
                detail: this.translateService.instant('messages.accessKeyDisabled')
              })
            }
          },
          (e) => {
            this.loggerService.error(e);
            this.form.reset();
            this.updateModified(false);
            this.showPrivateKeyFormDialog = false;
            this.userVerifyReady = false;
            this.checkUserVerification();
          }
        )
      }
    }
  }

  closeDialog() {
    this.resetInactivityMonitor();
    this.form.reset();
    this.localStorageService.setUserVerified("");
    this.updateModified(false);
    this.showPrivateKeyFormDialog = false;
    this.userVerifyReady = false;
  }

  isTenantValid(): boolean {
    const result: ValidTenantResult = this.checkPermissionsService.isTenantValid(this.konektorProperties);
    this.disabledToolTip = result.message;
    return result.valid;
  }

  addMessage(message: string) {
    this.messageService.add({
      severity: 'warn',
      summary: this.translateService.instant('messages.warning'),
      detail: this.translateService.instant(message)
    })
  }
}