<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeDialog()"
></app-inactivity-monitor>
<p-toast />
<div *ngIf="!userIsVerified && !loading" class="flex flex-column justify-content-center mb-2">
    <div class="messageWarning flex flex-row align-content-center justify-content-start p-2 py-3 gap-2" [key]="'important'">
        <i class="flex flex-column justify-content-center pi pi-exclamation-triangle mr-2 m-2" style="font-size: 1.5rem"></i>
        <!-- <div class="flex flex-column justify-content-center">{{ 'titles.important' | translate }}</div> -->
        <div class="flex flex-column justify-content-center">{{ ('messages.verification_required' + subjectType) | translate }}</div>
        <div *ngIf="verifyReady" class="flex flex-column justify-content-center font-bold underline cursor-pointer" (click)="isTenantValid() ? verifyUser() : addMessage(disabledToolTip)">{{ 'verify_user' | translate }}.</div>
    </div>
</div>

<!-- Widget User Verify -->
<app-widget-match
    [numId]="userNumId"
    [subject]="user"
    [widgetUrl]="widgetUrl"
    [verified]="userIsVerified"
    [managerSettings]="managerSettings"
    [konektorProperties]="konektorProperties"
    [ready]="userVerifyReady"
    [getToken]="true"
    [isLoggedUserVerification]="true"
    (result)="onUserWidgetMatchResult($event)"
></app-widget-match>

<p-dialog
    [(visible)]="showPrivateKeyFormDialog" [modal]="true" [style]="{ background: '#FFFFFF', 'border-radius': '6px', width: '25rem' }" [draggable]="false" [resizable]="false">
    <ng-template pTemplate="headless">
        <div class="dialogHeader mt-3 ml-3">{{ 'content.oneTimePasswordVerification' | translate }}</div>
        <div class="flex flex-column align-items-center justify-content-center gap-3 my-3" [formGroup]="form">
            <span class="p-text-secondary block mb-3">{{ 'content.enterAccessKey' | translate }}</span>
            <p-password styleClass="p-password p-component p-inputwrapper p-input-icon-right"
                    [inputStyle]="{'width': '100%'}" [style]="{'width': '-webkit-fill-available'}"
                    formControlName="privateKey" [feedback]="false" [toggleMask]="true"
                    [ngClass]="invalidKey ? 'ng-invalid ng-dirty':'' "
                    (input)="setPrivateKeyFormDialogTimeout()"
                />
                <small *ngIf="!isValid('privateKey') || invalidKey"
                    [style]="{'color': 'red'}">{{ (!isValid('privateKey') ? 'titles.error_requiredField' : invalidKey ? 'messages.invalidAccessKey' : '') | translate }}</small>
            <div class="flex justify-content-center gap-2">
                <button pButton label="{{ 'cancel' | translate }}" class="p-button-text" (click)="closeDialog()"></button>
                <p-button label="{{ 'verify' | translate}}" severity="secondary" (onClick)="onDialogSubmit()" />
            </div>
        </div>
    </ng-template>
</p-dialog>