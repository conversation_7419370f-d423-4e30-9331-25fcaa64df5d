#!/bin/bash

# Simple emergency fix script for the missing configuration script
# This creates the script step by step to avoid complex quoting issues

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_color $GREEN "=== Simple Emergency Fix Script ==="

# Check if Docker Compose is available
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
elif docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    print_color $RED "Docker Compose not found!"
    exit 1
fi

# Get container name
CONTAINER_NAME=$($DOCKER_COMPOSE ps -q angular-app 2>/dev/null)

if [[ -z "$CONTAINER_NAME" ]]; then
    print_color $RED "Container not running. Starting it first..."
    $DOCKER_COMPOSE up -d
    sleep 5
    CONTAINER_NAME=$($DOCKER_COMPOSE ps -q angular-app 2>/dev/null)
    
    if [[ -z "$CONTAINER_NAME" ]]; then
        print_color $RED "Failed to start container!"
        exit 1
    fi
fi

print_color $CYAN "Container ID: $CONTAINER_NAME"

# 1. Install jq if missing
print_color $YELLOW "1. Ensuring jq is available..."
docker exec "$CONTAINER_NAME" sh -c "which jq || apk add --no-cache jq"

# 2. Create the configuration script line by line
print_color $YELLOW "2. Creating configuration script..."

# Create the script header
docker exec "$CONTAINER_NAME" sh -c 'echo "#!/bin/bash" > /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "# Configuration script for Verazial Admin" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "" >> /usr/local/bin/configure-environment.sh'

# Add variables
docker exec "$CONTAINER_NAME" sh -c 'echo "CONFIG_FILE=\"/app/config/deployment-config.json\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "TARGET_DIR=\"/usr/share/nginx/html\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "" >> /usr/local/bin/configure-environment.sh'

# Add main logic
docker exec "$CONTAINER_NAME" sh -c 'echo "echo \"Starting environment configuration...\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "" >> /usr/local/bin/configure-environment.sh'

# Check if config file exists
docker exec "$CONTAINER_NAME" sh -c 'echo "if [ ! -f \"\$CONFIG_FILE\" ]; then" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "    echo \"Configuration file not found at \$CONFIG_FILE\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "    echo \"Using default environment settings\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "    exit 0" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "fi" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "" >> /usr/local/bin/configure-environment.sh'

# Read configuration
docker exec "$CONTAINER_NAME" sh -c 'echo "echo \"Reading configuration from \$CONFIG_FILE\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "" >> /usr/local/bin/configure-environment.sh'

# Extract values
docker exec "$CONTAINER_NAME" sh -c 'echo "GRPC_API_GATEWAY=\$(jq -r \".grpcApiGateway\" \"\$CONFIG_FILE\")" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "KONEKTOR_API=\$(jq -r \".konektorAPI\" \"\$CONFIG_FILE\")" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "CREDENTIALS_API=\$(jq -r \".credentialsAPI\" \"\$CONFIG_FILE\")" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "CREDENTIAL_USER=\$(jq -r \".credential_user\" \"\$CONFIG_FILE\")" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "CREDENTIAL_PASSWORD=\$(jq -r \".credential_password\" \"\$CONFIG_FILE\")" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "" >> /usr/local/bin/configure-environment.sh'

# Display configuration
docker exec "$CONTAINER_NAME" sh -c 'echo "echo \"Applying configuration:\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "echo \"  gRPC API Gateway: \$GRPC_API_GATEWAY\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "echo \"  Konektor API: \$KONEKTOR_API\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "echo \"  Credentials API: \$CREDENTIALS_API\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "echo \"  Credential User: \$CREDENTIAL_USER\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "" >> /usr/local/bin/configure-environment.sh'

# Add the replacement logic
docker exec "$CONTAINER_NAME" sh -c 'echo "find \"\$TARGET_DIR\" -name \"*.js\" -type f | while read -r file; do" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "    echo \"Processing file: \$file\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "    sed -i \"s|__GRPC_API_GATEWAY__|\$GRPC_API_GATEWAY|g\" \"\$file\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "    sed -i \"s|__KONEKTOR_API__|\$KONEKTOR_API|g\" \"\$file\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "    sed -i \"s|__CREDENTIALS_API__|\$CREDENTIALS_API|g\" \"\$file\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "    sed -i \"s|__CREDENTIAL_USER__|\$CREDENTIAL_USER|g\" \"\$file\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "    sed -i \"s|__CREDENTIAL_PASSWORD__|\$CREDENTIAL_PASSWORD|g\" \"\$file\"" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "done" >> /usr/local/bin/configure-environment.sh'
docker exec "$CONTAINER_NAME" sh -c 'echo "" >> /usr/local/bin/configure-environment.sh'

# Add completion message
docker exec "$CONTAINER_NAME" sh -c 'echo "echo \"Environment configuration completed successfully!\"" >> /usr/local/bin/configure-environment.sh'

# 3. Make the script executable
print_color $YELLOW "3. Making script executable..."
docker exec "$CONTAINER_NAME" chmod +x /usr/local/bin/configure-environment.sh

# 4. Check if configuration file is mounted
print_color $YELLOW "4. Checking configuration file..."
if docker exec "$CONTAINER_NAME" test -f /app/config/deployment-config.json; then
    print_color $GREEN "✓ Configuration file is mounted"
else
    print_color $RED "✗ Configuration file not found!"
    print_color $YELLOW "Please ensure deployment-config.json is in the current directory and restart the container"
    exit 1
fi

# 5. Run the configuration script
print_color $YELLOW "5. Running configuration script..."
docker exec "$CONTAINER_NAME" /usr/local/bin/configure-environment.sh

# 6. Verify configuration was applied
print_color $YELLOW "6. Verifying configuration was applied..."
CONFIG_VALUE=$(docker exec "$CONTAINER_NAME" jq -r '.grpcApiGateway' /app/config/deployment-config.json 2>/dev/null)

if [[ -n "$CONFIG_VALUE" && "$CONFIG_VALUE" != "null" ]]; then
    if docker exec "$CONTAINER_NAME" find /usr/share/nginx/html -name "*.js" -exec grep -l "$CONFIG_VALUE" {} \; | head -1 | grep -q .; then
        print_color $GREEN "✓ Configuration values found in JavaScript files"
    else
        print_color $YELLOW "Configuration values not found in JS files. Checking for placeholders..."
        if docker exec "$CONTAINER_NAME" find /usr/share/nginx/html -name "*.js" -exec grep -l "__GRPC_API_GATEWAY__" {} \; | head -1 | grep -q .; then
            print_color $RED "✗ Placeholders still found. Configuration may not have been applied correctly."
        else
            print_color $GREEN "✓ No placeholders found - configuration likely applied"
        fi
    fi
else
    print_color $YELLOW "Could not read configuration value for verification"
fi

echo ""
print_color $GREEN "=== Fix Completed! ==="
print_color $CYAN "The configuration script has been created and executed."

echo ""
print_color $YELLOW "To test the application:"
print_color $WHITE "1. Check container logs: $DOCKER_COMPOSE logs angular-app"
print_color $WHITE "2. Test in browser: http://localhost:\$(jq -r '.port' deployment-config.json)"
print_color $WHITE "3. Check browser developer tools for API calls"

echo ""
print_color $YELLOW "If you need to run the configuration again:"
print_color $WHITE "docker exec $CONTAINER_NAME /usr/local/bin/configure-environment.sh"
