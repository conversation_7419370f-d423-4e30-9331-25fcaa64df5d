// source: criminalistics/criminalistics.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var google_protobuf_timestamp_pb = require('google-protobuf/google/protobuf/timestamp_pb.js');
goog.object.extend(proto, google_protobuf_timestamp_pb);
var google_protobuf_empty_pb = require('google-protobuf/google/protobuf/empty_pb.js');
goog.object.extend(proto, google_protobuf_empty_pb);
var failure_pb = require('../failure_pb.js');
goog.object.extend(proto, failure_pb);
var util_pb = require('../util_pb.js');
goog.object.extend(proto, util_pb);
goog.exportSymbol('proto.ArrayOfCase', null, global);
goog.exportSymbol('proto.ArrayOfCaseDetailGrpcModel', null, global);
goog.exportSymbol('proto.ArrayOfCaseLocationGrpcModel', null, global);
goog.exportSymbol('proto.ArrayOfRelatedCaseGrpcModel', null, global);
goog.exportSymbol('proto.ArrayOfSubjectRelatedCaseGrpcModel', null, global);
goog.exportSymbol('proto.CaseCoincidenceGrpcModel', null, global);
goog.exportSymbol('proto.CaseCommentGrpcModel', null, global);
goog.exportSymbol('proto.CaseDetailGrpcModel', null, global);
goog.exportSymbol('proto.CaseEvidenceGrpcModel', null, global);
goog.exportSymbol('proto.CaseGeneralInfoGrpcModel', null, global);
goog.exportSymbol('proto.CaseGrpcModel', null, global);
goog.exportSymbol('proto.CaseLocationGrpcModel', null, global);
goog.exportSymbol('proto.CriminalisticCaseCoincidenceResponse', null, global);
goog.exportSymbol('proto.CriminalisticCaseCoincidenceResponse.ResponseCase', null, global);
goog.exportSymbol('proto.CriminalisticCaseCommentResponse', null, global);
goog.exportSymbol('proto.CriminalisticCaseCommentResponse.ResponseCase', null, global);
goog.exportSymbol('proto.CriminalisticCaseDetailResponse', null, global);
goog.exportSymbol('proto.CriminalisticCaseDetailResponse.ResponseCase', null, global);
goog.exportSymbol('proto.CriminalisticCaseEvidenceResponse', null, global);
goog.exportSymbol('proto.CriminalisticCaseEvidenceResponse.ResponseCase', null, global);
goog.exportSymbol('proto.CriminalisticCaseGeneralInfoResponse', null, global);
goog.exportSymbol('proto.CriminalisticCaseGeneralInfoResponse.ResponseCase', null, global);
goog.exportSymbol('proto.CriminalisticCaseResponse', null, global);
goog.exportSymbol('proto.CriminalisticCaseResponse.ResponseCase', null, global);
goog.exportSymbol('proto.CriminalisticLocationCasesResponse', null, global);
goog.exportSymbol('proto.CriminalisticLocationCasesResponse.ResponseCase', null, global);
goog.exportSymbol('proto.CriminalisticRelatedCasesResponse', null, global);
goog.exportSymbol('proto.CriminalisticRelatedCasesResponse.ResponseCase', null, global);
goog.exportSymbol('proto.CriminalisticSubjectRelatedCasesResponse', null, global);
goog.exportSymbol('proto.CriminalisticSubjectRelatedCasesResponse.ResponseCase', null, global);
goog.exportSymbol('proto.NumberResponse', null, global);
goog.exportSymbol('proto.NumberResponse.ResponseCase', null, global);
goog.exportSymbol('proto.RelatedCaseGrpcModel', null, global);
goog.exportSymbol('proto.ResponseCaseLocation', null, global);
goog.exportSymbol('proto.ResponseCaseLocation.ResponseCase', null, global);
goog.exportSymbol('proto.ResponseRelatedCase', null, global);
goog.exportSymbol('proto.ResponseRelatedCase.ResponseCase', null, global);
goog.exportSymbol('proto.ResponseSubjectRelatedCase', null, global);
goog.exportSymbol('proto.ResponseSubjectRelatedCase.ResponseCase', null, global);
goog.exportSymbol('proto.SubjectRelatedCaseGrpcModel', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CaseGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.CaseGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CaseGrpcModel.displayName = 'proto.CaseGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ArrayOfCase = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ArrayOfCase.repeatedFields_, null);
};
goog.inherits(proto.ArrayOfCase, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ArrayOfCase.displayName = 'proto.ArrayOfCase';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CaseGeneralInfoGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.CaseGeneralInfoGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CaseGeneralInfoGrpcModel.displayName = 'proto.CaseGeneralInfoGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CriminalisticCaseResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.CriminalisticCaseResponse.oneofGroups_);
};
goog.inherits(proto.CriminalisticCaseResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CriminalisticCaseResponse.displayName = 'proto.CriminalisticCaseResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CriminalisticCaseGeneralInfoResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.CriminalisticCaseGeneralInfoResponse.oneofGroups_);
};
goog.inherits(proto.CriminalisticCaseGeneralInfoResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CriminalisticCaseGeneralInfoResponse.displayName = 'proto.CriminalisticCaseGeneralInfoResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CaseLocationGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.CaseLocationGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CaseLocationGrpcModel.displayName = 'proto.CaseLocationGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ArrayOfCaseLocationGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ArrayOfCaseLocationGrpcModel.repeatedFields_, null);
};
goog.inherits(proto.ArrayOfCaseLocationGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ArrayOfCaseLocationGrpcModel.displayName = 'proto.ArrayOfCaseLocationGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ResponseCaseLocation = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ResponseCaseLocation.oneofGroups_);
};
goog.inherits(proto.ResponseCaseLocation, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ResponseCaseLocation.displayName = 'proto.ResponseCaseLocation';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.RelatedCaseGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.RelatedCaseGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.RelatedCaseGrpcModel.displayName = 'proto.RelatedCaseGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ArrayOfRelatedCaseGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ArrayOfRelatedCaseGrpcModel.repeatedFields_, null);
};
goog.inherits(proto.ArrayOfRelatedCaseGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ArrayOfRelatedCaseGrpcModel.displayName = 'proto.ArrayOfRelatedCaseGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CaseDetailGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.CaseDetailGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CaseDetailGrpcModel.displayName = 'proto.CaseDetailGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ArrayOfCaseDetailGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ArrayOfCaseDetailGrpcModel.repeatedFields_, null);
};
goog.inherits(proto.ArrayOfCaseDetailGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ArrayOfCaseDetailGrpcModel.displayName = 'proto.ArrayOfCaseDetailGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ResponseRelatedCase = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ResponseRelatedCase.oneofGroups_);
};
goog.inherits(proto.ResponseRelatedCase, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ResponseRelatedCase.displayName = 'proto.ResponseRelatedCase';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.SubjectRelatedCaseGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.SubjectRelatedCaseGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.SubjectRelatedCaseGrpcModel.displayName = 'proto.SubjectRelatedCaseGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ArrayOfSubjectRelatedCaseGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.ArrayOfSubjectRelatedCaseGrpcModel.repeatedFields_, null);
};
goog.inherits(proto.ArrayOfSubjectRelatedCaseGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ArrayOfSubjectRelatedCaseGrpcModel.displayName = 'proto.ArrayOfSubjectRelatedCaseGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.ResponseSubjectRelatedCase = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.ResponseSubjectRelatedCase.oneofGroups_);
};
goog.inherits(proto.ResponseSubjectRelatedCase, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.ResponseSubjectRelatedCase.displayName = 'proto.ResponseSubjectRelatedCase';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CaseEvidenceGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.CaseEvidenceGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CaseEvidenceGrpcModel.displayName = 'proto.CaseEvidenceGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CaseCommentGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.CaseCommentGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CaseCommentGrpcModel.displayName = 'proto.CaseCommentGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CaseCoincidenceGrpcModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.CaseCoincidenceGrpcModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CaseCoincidenceGrpcModel.displayName = 'proto.CaseCoincidenceGrpcModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CriminalisticCaseCommentResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.CriminalisticCaseCommentResponse.oneofGroups_);
};
goog.inherits(proto.CriminalisticCaseCommentResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CriminalisticCaseCommentResponse.displayName = 'proto.CriminalisticCaseCommentResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CriminalisticCaseEvidenceResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.CriminalisticCaseEvidenceResponse.oneofGroups_);
};
goog.inherits(proto.CriminalisticCaseEvidenceResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CriminalisticCaseEvidenceResponse.displayName = 'proto.CriminalisticCaseEvidenceResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CriminalisticCaseDetailResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.CriminalisticCaseDetailResponse.oneofGroups_);
};
goog.inherits(proto.CriminalisticCaseDetailResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CriminalisticCaseDetailResponse.displayName = 'proto.CriminalisticCaseDetailResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CriminalisticRelatedCasesResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.CriminalisticRelatedCasesResponse.oneofGroups_);
};
goog.inherits(proto.CriminalisticRelatedCasesResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CriminalisticRelatedCasesResponse.displayName = 'proto.CriminalisticRelatedCasesResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CriminalisticSubjectRelatedCasesResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.CriminalisticSubjectRelatedCasesResponse.oneofGroups_);
};
goog.inherits(proto.CriminalisticSubjectRelatedCasesResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CriminalisticSubjectRelatedCasesResponse.displayName = 'proto.CriminalisticSubjectRelatedCasesResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CriminalisticLocationCasesResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.CriminalisticLocationCasesResponse.oneofGroups_);
};
goog.inherits(proto.CriminalisticLocationCasesResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CriminalisticLocationCasesResponse.displayName = 'proto.CriminalisticLocationCasesResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.NumberResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.NumberResponse.oneofGroups_);
};
goog.inherits(proto.NumberResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.NumberResponse.displayName = 'proto.NumberResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.CriminalisticCaseCoincidenceResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.CriminalisticCaseCoincidenceResponse.oneofGroups_);
};
goog.inherits(proto.CriminalisticCaseCoincidenceResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.CriminalisticCaseCoincidenceResponse.displayName = 'proto.CriminalisticCaseCoincidenceResponse';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CaseGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.CaseGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CaseGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
id: (f = jspb.Message.getField(msg, 1)) == null ? undefined : f,
name: (f = jspb.Message.getField(msg, 2)) == null ? undefined : f,
number: (f = jspb.Message.getField(msg, 3)) == null ? undefined : f,
description: (f = jspb.Message.getField(msg, 4)) == null ? undefined : f,
status: (f = jspb.Message.getField(msg, 5)) == null ? undefined : f,
createdby: (f = jspb.Message.getField(msg, 6)) == null ? undefined : f,
updatedby: (f = jspb.Message.getField(msg, 7)) == null ? undefined : f,
crimedate: (f = msg.getCrimedate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
opendate: (f = msg.getOpendate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
closedate: (f = msg.getClosedate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
casedetail: (f = msg.getCasedetail()) && proto.ArrayOfCaseDetailGrpcModel.toObject(includeInstance, f),
relatedcases: (f = msg.getRelatedcases()) && proto.ArrayOfRelatedCaseGrpcModel.toObject(includeInstance, f),
relatedsubjects: (f = msg.getRelatedsubjects()) && proto.ArrayOfSubjectRelatedCaseGrpcModel.toObject(includeInstance, f),
locations: (f = msg.getLocations()) && proto.ArrayOfCaseLocationGrpcModel.toObject(includeInstance, f),
createdat: (f = msg.getCreatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
updatedat: (f = msg.getUpdatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CaseGrpcModel}
 */
proto.CaseGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CaseGrpcModel;
  return proto.CaseGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CaseGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CaseGrpcModel}
 */
proto.CaseGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setNumber(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDescription(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setStatus(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setCreatedby(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setUpdatedby(value);
      break;
    case 8:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCrimedate(value);
      break;
    case 9:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setOpendate(value);
      break;
    case 10:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setClosedate(value);
      break;
    case 11:
      var value = new proto.ArrayOfCaseDetailGrpcModel;
      reader.readMessage(value,proto.ArrayOfCaseDetailGrpcModel.deserializeBinaryFromReader);
      msg.setCasedetail(value);
      break;
    case 12:
      var value = new proto.ArrayOfRelatedCaseGrpcModel;
      reader.readMessage(value,proto.ArrayOfRelatedCaseGrpcModel.deserializeBinaryFromReader);
      msg.setRelatedcases(value);
      break;
    case 13:
      var value = new proto.ArrayOfSubjectRelatedCaseGrpcModel;
      reader.readMessage(value,proto.ArrayOfSubjectRelatedCaseGrpcModel.deserializeBinaryFromReader);
      msg.setRelatedsubjects(value);
      break;
    case 14:
      var value = new proto.ArrayOfCaseLocationGrpcModel;
      reader.readMessage(value,proto.ArrayOfCaseLocationGrpcModel.deserializeBinaryFromReader);
      msg.setLocations(value);
      break;
    case 15:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCreatedat(value);
      break;
    case 16:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setUpdatedat(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CaseGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CaseGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CaseGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeString(
      5,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeString(
      6,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 7));
  if (f != null) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getCrimedate();
  if (f != null) {
    writer.writeMessage(
      8,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getOpendate();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getClosedate();
  if (f != null) {
    writer.writeMessage(
      10,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getCasedetail();
  if (f != null) {
    writer.writeMessage(
      11,
      f,
      proto.ArrayOfCaseDetailGrpcModel.serializeBinaryToWriter
    );
  }
  f = message.getRelatedcases();
  if (f != null) {
    writer.writeMessage(
      12,
      f,
      proto.ArrayOfRelatedCaseGrpcModel.serializeBinaryToWriter
    );
  }
  f = message.getRelatedsubjects();
  if (f != null) {
    writer.writeMessage(
      13,
      f,
      proto.ArrayOfSubjectRelatedCaseGrpcModel.serializeBinaryToWriter
    );
  }
  f = message.getLocations();
  if (f != null) {
    writer.writeMessage(
      14,
      f,
      proto.ArrayOfCaseLocationGrpcModel.serializeBinaryToWriter
    );
  }
  f = message.getCreatedat();
  if (f != null) {
    writer.writeMessage(
      15,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getUpdatedat();
  if (f != null) {
    writer.writeMessage(
      16,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.CaseGrpcModel.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.setId = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearId = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasId = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.CaseGrpcModel.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.setName = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearName = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasName = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string number = 3;
 * @return {string}
 */
proto.CaseGrpcModel.prototype.getNumber = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.setNumber = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearNumber = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasNumber = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string description = 4;
 * @return {string}
 */
proto.CaseGrpcModel.prototype.getDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.setDescription = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearDescription = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasDescription = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional string status = 5;
 * @return {string}
 */
proto.CaseGrpcModel.prototype.getStatus = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.setStatus = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearStatus = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasStatus = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional string createdBy = 6;
 * @return {string}
 */
proto.CaseGrpcModel.prototype.getCreatedby = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.setCreatedby = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearCreatedby = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasCreatedby = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional string updatedBy = 7;
 * @return {string}
 */
proto.CaseGrpcModel.prototype.getUpdatedby = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.setUpdatedby = function(value) {
  return jspb.Message.setField(this, 7, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearUpdatedby = function() {
  return jspb.Message.setField(this, 7, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasUpdatedby = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional google.protobuf.Timestamp crimeDate = 8;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseGrpcModel.prototype.getCrimedate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 8));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseGrpcModel} returns this
*/
proto.CaseGrpcModel.prototype.setCrimedate = function(value) {
  return jspb.Message.setWrapperField(this, 8, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearCrimedate = function() {
  return this.setCrimedate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasCrimedate = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional google.protobuf.Timestamp openDate = 9;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseGrpcModel.prototype.getOpendate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 9));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseGrpcModel} returns this
*/
proto.CaseGrpcModel.prototype.setOpendate = function(value) {
  return jspb.Message.setWrapperField(this, 9, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearOpendate = function() {
  return this.setOpendate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasOpendate = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional google.protobuf.Timestamp closeDate = 10;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseGrpcModel.prototype.getClosedate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 10));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseGrpcModel} returns this
*/
proto.CaseGrpcModel.prototype.setClosedate = function(value) {
  return jspb.Message.setWrapperField(this, 10, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearClosedate = function() {
  return this.setClosedate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasClosedate = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional ArrayOfCaseDetailGrpcModel caseDetail = 11;
 * @return {?proto.ArrayOfCaseDetailGrpcModel}
 */
proto.CaseGrpcModel.prototype.getCasedetail = function() {
  return /** @type{?proto.ArrayOfCaseDetailGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.ArrayOfCaseDetailGrpcModel, 11));
};


/**
 * @param {?proto.ArrayOfCaseDetailGrpcModel|undefined} value
 * @return {!proto.CaseGrpcModel} returns this
*/
proto.CaseGrpcModel.prototype.setCasedetail = function(value) {
  return jspb.Message.setWrapperField(this, 11, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearCasedetail = function() {
  return this.setCasedetail(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasCasedetail = function() {
  return jspb.Message.getField(this, 11) != null;
};


/**
 * optional ArrayOfRelatedCaseGrpcModel relatedCases = 12;
 * @return {?proto.ArrayOfRelatedCaseGrpcModel}
 */
proto.CaseGrpcModel.prototype.getRelatedcases = function() {
  return /** @type{?proto.ArrayOfRelatedCaseGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.ArrayOfRelatedCaseGrpcModel, 12));
};


/**
 * @param {?proto.ArrayOfRelatedCaseGrpcModel|undefined} value
 * @return {!proto.CaseGrpcModel} returns this
*/
proto.CaseGrpcModel.prototype.setRelatedcases = function(value) {
  return jspb.Message.setWrapperField(this, 12, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearRelatedcases = function() {
  return this.setRelatedcases(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasRelatedcases = function() {
  return jspb.Message.getField(this, 12) != null;
};


/**
 * optional ArrayOfSubjectRelatedCaseGrpcModel relatedSubjects = 13;
 * @return {?proto.ArrayOfSubjectRelatedCaseGrpcModel}
 */
proto.CaseGrpcModel.prototype.getRelatedsubjects = function() {
  return /** @type{?proto.ArrayOfSubjectRelatedCaseGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.ArrayOfSubjectRelatedCaseGrpcModel, 13));
};


/**
 * @param {?proto.ArrayOfSubjectRelatedCaseGrpcModel|undefined} value
 * @return {!proto.CaseGrpcModel} returns this
*/
proto.CaseGrpcModel.prototype.setRelatedsubjects = function(value) {
  return jspb.Message.setWrapperField(this, 13, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearRelatedsubjects = function() {
  return this.setRelatedsubjects(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasRelatedsubjects = function() {
  return jspb.Message.getField(this, 13) != null;
};


/**
 * optional ArrayOfCaseLocationGrpcModel locations = 14;
 * @return {?proto.ArrayOfCaseLocationGrpcModel}
 */
proto.CaseGrpcModel.prototype.getLocations = function() {
  return /** @type{?proto.ArrayOfCaseLocationGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.ArrayOfCaseLocationGrpcModel, 14));
};


/**
 * @param {?proto.ArrayOfCaseLocationGrpcModel|undefined} value
 * @return {!proto.CaseGrpcModel} returns this
*/
proto.CaseGrpcModel.prototype.setLocations = function(value) {
  return jspb.Message.setWrapperField(this, 14, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearLocations = function() {
  return this.setLocations(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasLocations = function() {
  return jspb.Message.getField(this, 14) != null;
};


/**
 * optional google.protobuf.Timestamp createdAt = 15;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseGrpcModel.prototype.getCreatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 15));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseGrpcModel} returns this
*/
proto.CaseGrpcModel.prototype.setCreatedat = function(value) {
  return jspb.Message.setWrapperField(this, 15, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearCreatedat = function() {
  return this.setCreatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasCreatedat = function() {
  return jspb.Message.getField(this, 15) != null;
};


/**
 * optional google.protobuf.Timestamp updatedAt = 16;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseGrpcModel.prototype.getUpdatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 16));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseGrpcModel} returns this
*/
proto.CaseGrpcModel.prototype.setUpdatedat = function(value) {
  return jspb.Message.setWrapperField(this, 16, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGrpcModel} returns this
 */
proto.CaseGrpcModel.prototype.clearUpdatedat = function() {
  return this.setUpdatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGrpcModel.prototype.hasUpdatedat = function() {
  return jspb.Message.getField(this, 16) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ArrayOfCase.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ArrayOfCase.prototype.toObject = function(opt_includeInstance) {
  return proto.ArrayOfCase.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ArrayOfCase} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfCase.toObject = function(includeInstance, msg) {
  var f, obj = {
elementList: jspb.Message.toObjectList(msg.getElementList(),
    proto.CaseGrpcModel.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ArrayOfCase}
 */
proto.ArrayOfCase.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ArrayOfCase;
  return proto.ArrayOfCase.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ArrayOfCase} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ArrayOfCase}
 */
proto.ArrayOfCase.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.CaseGrpcModel;
      reader.readMessage(value,proto.CaseGrpcModel.deserializeBinaryFromReader);
      msg.addElement(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ArrayOfCase.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ArrayOfCase.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ArrayOfCase} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfCase.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getElementList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.CaseGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * repeated CaseGrpcModel element = 1;
 * @return {!Array<!proto.CaseGrpcModel>}
 */
proto.ArrayOfCase.prototype.getElementList = function() {
  return /** @type{!Array<!proto.CaseGrpcModel>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.CaseGrpcModel, 1));
};


/**
 * @param {!Array<!proto.CaseGrpcModel>} value
 * @return {!proto.ArrayOfCase} returns this
*/
proto.ArrayOfCase.prototype.setElementList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.CaseGrpcModel=} opt_value
 * @param {number=} opt_index
 * @return {!proto.CaseGrpcModel}
 */
proto.ArrayOfCase.prototype.addElement = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.CaseGrpcModel, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ArrayOfCase} returns this
 */
proto.ArrayOfCase.prototype.clearElementList = function() {
  return this.setElementList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CaseGeneralInfoGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.CaseGeneralInfoGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CaseGeneralInfoGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseGeneralInfoGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
id: (f = jspb.Message.getField(msg, 1)) == null ? undefined : f,
name: (f = jspb.Message.getField(msg, 2)) == null ? undefined : f,
number: (f = jspb.Message.getField(msg, 3)) == null ? undefined : f,
description: (f = jspb.Message.getField(msg, 4)) == null ? undefined : f,
status: (f = jspb.Message.getField(msg, 5)) == null ? undefined : f,
createdby: (f = jspb.Message.getField(msg, 6)) == null ? undefined : f,
updatedby: (f = jspb.Message.getField(msg, 7)) == null ? undefined : f,
crimedate: (f = msg.getCrimedate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
opendate: (f = msg.getOpendate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
closedate: (f = msg.getClosedate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
createdat: (f = msg.getCreatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
updatedat: (f = msg.getUpdatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CaseGeneralInfoGrpcModel}
 */
proto.CaseGeneralInfoGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CaseGeneralInfoGrpcModel;
  return proto.CaseGeneralInfoGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CaseGeneralInfoGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CaseGeneralInfoGrpcModel}
 */
proto.CaseGeneralInfoGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setNumber(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDescription(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setStatus(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setCreatedby(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setUpdatedby(value);
      break;
    case 8:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCrimedate(value);
      break;
    case 9:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setOpendate(value);
      break;
    case 10:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setClosedate(value);
      break;
    case 11:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCreatedat(value);
      break;
    case 12:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setUpdatedat(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CaseGeneralInfoGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CaseGeneralInfoGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CaseGeneralInfoGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseGeneralInfoGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeString(
      5,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeString(
      6,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 7));
  if (f != null) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getCrimedate();
  if (f != null) {
    writer.writeMessage(
      8,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getOpendate();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getClosedate();
  if (f != null) {
    writer.writeMessage(
      10,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getCreatedat();
  if (f != null) {
    writer.writeMessage(
      11,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getUpdatedat();
  if (f != null) {
    writer.writeMessage(
      12,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.CaseGeneralInfoGrpcModel.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.setId = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.clearId = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGeneralInfoGrpcModel.prototype.hasId = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.CaseGeneralInfoGrpcModel.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.setName = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.clearName = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGeneralInfoGrpcModel.prototype.hasName = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string number = 3;
 * @return {string}
 */
proto.CaseGeneralInfoGrpcModel.prototype.getNumber = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.setNumber = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.clearNumber = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGeneralInfoGrpcModel.prototype.hasNumber = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string description = 4;
 * @return {string}
 */
proto.CaseGeneralInfoGrpcModel.prototype.getDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.setDescription = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.clearDescription = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGeneralInfoGrpcModel.prototype.hasDescription = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional string status = 5;
 * @return {string}
 */
proto.CaseGeneralInfoGrpcModel.prototype.getStatus = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.setStatus = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.clearStatus = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGeneralInfoGrpcModel.prototype.hasStatus = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional string createdBy = 6;
 * @return {string}
 */
proto.CaseGeneralInfoGrpcModel.prototype.getCreatedby = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.setCreatedby = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.clearCreatedby = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGeneralInfoGrpcModel.prototype.hasCreatedby = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional string updatedBy = 7;
 * @return {string}
 */
proto.CaseGeneralInfoGrpcModel.prototype.getUpdatedby = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.setUpdatedby = function(value) {
  return jspb.Message.setField(this, 7, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.clearUpdatedby = function() {
  return jspb.Message.setField(this, 7, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGeneralInfoGrpcModel.prototype.hasUpdatedby = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional google.protobuf.Timestamp crimeDate = 8;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseGeneralInfoGrpcModel.prototype.getCrimedate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 8));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
*/
proto.CaseGeneralInfoGrpcModel.prototype.setCrimedate = function(value) {
  return jspb.Message.setWrapperField(this, 8, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.clearCrimedate = function() {
  return this.setCrimedate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGeneralInfoGrpcModel.prototype.hasCrimedate = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional google.protobuf.Timestamp openDate = 9;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseGeneralInfoGrpcModel.prototype.getOpendate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 9));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
*/
proto.CaseGeneralInfoGrpcModel.prototype.setOpendate = function(value) {
  return jspb.Message.setWrapperField(this, 9, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.clearOpendate = function() {
  return this.setOpendate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGeneralInfoGrpcModel.prototype.hasOpendate = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional google.protobuf.Timestamp closeDate = 10;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseGeneralInfoGrpcModel.prototype.getClosedate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 10));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
*/
proto.CaseGeneralInfoGrpcModel.prototype.setClosedate = function(value) {
  return jspb.Message.setWrapperField(this, 10, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.clearClosedate = function() {
  return this.setClosedate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGeneralInfoGrpcModel.prototype.hasClosedate = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional google.protobuf.Timestamp createdAt = 11;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseGeneralInfoGrpcModel.prototype.getCreatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 11));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
*/
proto.CaseGeneralInfoGrpcModel.prototype.setCreatedat = function(value) {
  return jspb.Message.setWrapperField(this, 11, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.clearCreatedat = function() {
  return this.setCreatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGeneralInfoGrpcModel.prototype.hasCreatedat = function() {
  return jspb.Message.getField(this, 11) != null;
};


/**
 * optional google.protobuf.Timestamp updatedAt = 12;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseGeneralInfoGrpcModel.prototype.getUpdatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 12));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
*/
proto.CaseGeneralInfoGrpcModel.prototype.setUpdatedat = function(value) {
  return jspb.Message.setWrapperField(this, 12, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseGeneralInfoGrpcModel} returns this
 */
proto.CaseGeneralInfoGrpcModel.prototype.clearUpdatedat = function() {
  return this.setUpdatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseGeneralInfoGrpcModel.prototype.hasUpdatedat = function() {
  return jspb.Message.getField(this, 12) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.CriminalisticCaseResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.CriminalisticCaseResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  FAILURE: 1,
  CASE: 2
};

/**
 * @return {proto.CriminalisticCaseResponse.ResponseCase}
 */
proto.CriminalisticCaseResponse.prototype.getResponseCase = function() {
  return /** @type {proto.CriminalisticCaseResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.CriminalisticCaseResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CriminalisticCaseResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.CriminalisticCaseResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CriminalisticCaseResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticCaseResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
failure: (f = msg.getFailure()) && failure_pb.FailureGrpc.toObject(includeInstance, f),
pb_case: (f = msg.getCase()) && proto.CaseGrpcModel.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CriminalisticCaseResponse}
 */
proto.CriminalisticCaseResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CriminalisticCaseResponse;
  return proto.CriminalisticCaseResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CriminalisticCaseResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CriminalisticCaseResponse}
 */
proto.CriminalisticCaseResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new failure_pb.FailureGrpc;
      reader.readMessage(value,failure_pb.FailureGrpc.deserializeBinaryFromReader);
      msg.setFailure(value);
      break;
    case 2:
      var value = new proto.CaseGrpcModel;
      reader.readMessage(value,proto.CaseGrpcModel.deserializeBinaryFromReader);
      msg.setCase(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CriminalisticCaseResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CriminalisticCaseResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CriminalisticCaseResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticCaseResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFailure();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      failure_pb.FailureGrpc.serializeBinaryToWriter
    );
  }
  f = message.getCase();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.CaseGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * optional FailureGrpc failure = 1;
 * @return {?proto.FailureGrpc}
 */
proto.CriminalisticCaseResponse.prototype.getFailure = function() {
  return /** @type{?proto.FailureGrpc} */ (
    jspb.Message.getWrapperField(this, failure_pb.FailureGrpc, 1));
};


/**
 * @param {?proto.FailureGrpc|undefined} value
 * @return {!proto.CriminalisticCaseResponse} returns this
*/
proto.CriminalisticCaseResponse.prototype.setFailure = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.CriminalisticCaseResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticCaseResponse} returns this
 */
proto.CriminalisticCaseResponse.prototype.clearFailure = function() {
  return this.setFailure(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticCaseResponse.prototype.hasFailure = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional CaseGrpcModel case = 2;
 * @return {?proto.CaseGrpcModel}
 */
proto.CriminalisticCaseResponse.prototype.getCase = function() {
  return /** @type{?proto.CaseGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.CaseGrpcModel, 2));
};


/**
 * @param {?proto.CaseGrpcModel|undefined} value
 * @return {!proto.CriminalisticCaseResponse} returns this
*/
proto.CriminalisticCaseResponse.prototype.setCase = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.CriminalisticCaseResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticCaseResponse} returns this
 */
proto.CriminalisticCaseResponse.prototype.clearCase = function() {
  return this.setCase(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticCaseResponse.prototype.hasCase = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.CriminalisticCaseGeneralInfoResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.CriminalisticCaseGeneralInfoResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  FAILURE: 1,
  CASE: 2
};

/**
 * @return {proto.CriminalisticCaseGeneralInfoResponse.ResponseCase}
 */
proto.CriminalisticCaseGeneralInfoResponse.prototype.getResponseCase = function() {
  return /** @type {proto.CriminalisticCaseGeneralInfoResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.CriminalisticCaseGeneralInfoResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CriminalisticCaseGeneralInfoResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.CriminalisticCaseGeneralInfoResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CriminalisticCaseGeneralInfoResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticCaseGeneralInfoResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
failure: (f = msg.getFailure()) && failure_pb.FailureGrpc.toObject(includeInstance, f),
pb_case: (f = msg.getCase()) && proto.CaseGeneralInfoGrpcModel.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CriminalisticCaseGeneralInfoResponse}
 */
proto.CriminalisticCaseGeneralInfoResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CriminalisticCaseGeneralInfoResponse;
  return proto.CriminalisticCaseGeneralInfoResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CriminalisticCaseGeneralInfoResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CriminalisticCaseGeneralInfoResponse}
 */
proto.CriminalisticCaseGeneralInfoResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new failure_pb.FailureGrpc;
      reader.readMessage(value,failure_pb.FailureGrpc.deserializeBinaryFromReader);
      msg.setFailure(value);
      break;
    case 2:
      var value = new proto.CaseGeneralInfoGrpcModel;
      reader.readMessage(value,proto.CaseGeneralInfoGrpcModel.deserializeBinaryFromReader);
      msg.setCase(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CriminalisticCaseGeneralInfoResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CriminalisticCaseGeneralInfoResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CriminalisticCaseGeneralInfoResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticCaseGeneralInfoResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFailure();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      failure_pb.FailureGrpc.serializeBinaryToWriter
    );
  }
  f = message.getCase();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.CaseGeneralInfoGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * optional FailureGrpc failure = 1;
 * @return {?proto.FailureGrpc}
 */
proto.CriminalisticCaseGeneralInfoResponse.prototype.getFailure = function() {
  return /** @type{?proto.FailureGrpc} */ (
    jspb.Message.getWrapperField(this, failure_pb.FailureGrpc, 1));
};


/**
 * @param {?proto.FailureGrpc|undefined} value
 * @return {!proto.CriminalisticCaseGeneralInfoResponse} returns this
*/
proto.CriminalisticCaseGeneralInfoResponse.prototype.setFailure = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.CriminalisticCaseGeneralInfoResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticCaseGeneralInfoResponse} returns this
 */
proto.CriminalisticCaseGeneralInfoResponse.prototype.clearFailure = function() {
  return this.setFailure(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticCaseGeneralInfoResponse.prototype.hasFailure = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional CaseGeneralInfoGrpcModel case = 2;
 * @return {?proto.CaseGeneralInfoGrpcModel}
 */
proto.CriminalisticCaseGeneralInfoResponse.prototype.getCase = function() {
  return /** @type{?proto.CaseGeneralInfoGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.CaseGeneralInfoGrpcModel, 2));
};


/**
 * @param {?proto.CaseGeneralInfoGrpcModel|undefined} value
 * @return {!proto.CriminalisticCaseGeneralInfoResponse} returns this
*/
proto.CriminalisticCaseGeneralInfoResponse.prototype.setCase = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.CriminalisticCaseGeneralInfoResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticCaseGeneralInfoResponse} returns this
 */
proto.CriminalisticCaseGeneralInfoResponse.prototype.clearCase = function() {
  return this.setCase(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticCaseGeneralInfoResponse.prototype.hasCase = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CaseLocationGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.CaseLocationGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CaseLocationGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseLocationGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
id: (f = jspb.Message.getField(msg, 1)) == null ? undefined : f,
caseid: (f = jspb.Message.getField(msg, 2)) == null ? undefined : f,
location: (f = jspb.Message.getField(msg, 3)) == null ? undefined : f,
name: (f = jspb.Message.getField(msg, 4)) == null ? undefined : f,
type: (f = jspb.Message.getField(msg, 5)) == null ? undefined : f,
comment: (f = jspb.Message.getField(msg, 6)) == null ? undefined : f,
description: (f = jspb.Message.getField(msg, 7)) == null ? undefined : f,
latitude: (f = jspb.Message.getField(msg, 8)) == null ? undefined : f,
longitude: (f = jspb.Message.getField(msg, 9)) == null ? undefined : f,
createdat: (f = msg.getCreatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
updatedat: (f = msg.getUpdatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CaseLocationGrpcModel}
 */
proto.CaseLocationGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CaseLocationGrpcModel;
  return proto.CaseLocationGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CaseLocationGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CaseLocationGrpcModel}
 */
proto.CaseLocationGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setCaseid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setLocation(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setType(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setComment(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setDescription(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setLatitude(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setLongitude(value);
      break;
    case 10:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCreatedat(value);
      break;
    case 11:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setUpdatedat(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CaseLocationGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CaseLocationGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CaseLocationGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseLocationGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeString(
      5,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeString(
      6,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 7));
  if (f != null) {
    writer.writeString(
      7,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 8));
  if (f != null) {
    writer.writeString(
      8,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 9));
  if (f != null) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getCreatedat();
  if (f != null) {
    writer.writeMessage(
      10,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getUpdatedat();
  if (f != null) {
    writer.writeMessage(
      11,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.CaseLocationGrpcModel.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.setId = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.clearId = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseLocationGrpcModel.prototype.hasId = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string caseId = 2;
 * @return {string}
 */
proto.CaseLocationGrpcModel.prototype.getCaseid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.setCaseid = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.clearCaseid = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseLocationGrpcModel.prototype.hasCaseid = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string location = 3;
 * @return {string}
 */
proto.CaseLocationGrpcModel.prototype.getLocation = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.setLocation = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.clearLocation = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseLocationGrpcModel.prototype.hasLocation = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string name = 4;
 * @return {string}
 */
proto.CaseLocationGrpcModel.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.setName = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.clearName = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseLocationGrpcModel.prototype.hasName = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional string type = 5;
 * @return {string}
 */
proto.CaseLocationGrpcModel.prototype.getType = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.setType = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.clearType = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseLocationGrpcModel.prototype.hasType = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional string comment = 6;
 * @return {string}
 */
proto.CaseLocationGrpcModel.prototype.getComment = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.setComment = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.clearComment = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseLocationGrpcModel.prototype.hasComment = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional string description = 7;
 * @return {string}
 */
proto.CaseLocationGrpcModel.prototype.getDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.setDescription = function(value) {
  return jspb.Message.setField(this, 7, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.clearDescription = function() {
  return jspb.Message.setField(this, 7, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseLocationGrpcModel.prototype.hasDescription = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional string latitude = 8;
 * @return {string}
 */
proto.CaseLocationGrpcModel.prototype.getLatitude = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.setLatitude = function(value) {
  return jspb.Message.setField(this, 8, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.clearLatitude = function() {
  return jspb.Message.setField(this, 8, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseLocationGrpcModel.prototype.hasLatitude = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional string longitude = 9;
 * @return {string}
 */
proto.CaseLocationGrpcModel.prototype.getLongitude = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.setLongitude = function(value) {
  return jspb.Message.setField(this, 9, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.clearLongitude = function() {
  return jspb.Message.setField(this, 9, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseLocationGrpcModel.prototype.hasLongitude = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional google.protobuf.Timestamp createdAt = 10;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseLocationGrpcModel.prototype.getCreatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 10));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseLocationGrpcModel} returns this
*/
proto.CaseLocationGrpcModel.prototype.setCreatedat = function(value) {
  return jspb.Message.setWrapperField(this, 10, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.clearCreatedat = function() {
  return this.setCreatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseLocationGrpcModel.prototype.hasCreatedat = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional google.protobuf.Timestamp updatedAt = 11;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseLocationGrpcModel.prototype.getUpdatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 11));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseLocationGrpcModel} returns this
*/
proto.CaseLocationGrpcModel.prototype.setUpdatedat = function(value) {
  return jspb.Message.setWrapperField(this, 11, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseLocationGrpcModel} returns this
 */
proto.CaseLocationGrpcModel.prototype.clearUpdatedat = function() {
  return this.setUpdatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseLocationGrpcModel.prototype.hasUpdatedat = function() {
  return jspb.Message.getField(this, 11) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ArrayOfCaseLocationGrpcModel.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ArrayOfCaseLocationGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.ArrayOfCaseLocationGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ArrayOfCaseLocationGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfCaseLocationGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
elementList: jspb.Message.toObjectList(msg.getElementList(),
    proto.CaseLocationGrpcModel.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ArrayOfCaseLocationGrpcModel}
 */
proto.ArrayOfCaseLocationGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ArrayOfCaseLocationGrpcModel;
  return proto.ArrayOfCaseLocationGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ArrayOfCaseLocationGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ArrayOfCaseLocationGrpcModel}
 */
proto.ArrayOfCaseLocationGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.CaseLocationGrpcModel;
      reader.readMessage(value,proto.CaseLocationGrpcModel.deserializeBinaryFromReader);
      msg.addElement(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ArrayOfCaseLocationGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ArrayOfCaseLocationGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ArrayOfCaseLocationGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfCaseLocationGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getElementList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.CaseLocationGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * repeated CaseLocationGrpcModel element = 1;
 * @return {!Array<!proto.CaseLocationGrpcModel>}
 */
proto.ArrayOfCaseLocationGrpcModel.prototype.getElementList = function() {
  return /** @type{!Array<!proto.CaseLocationGrpcModel>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.CaseLocationGrpcModel, 1));
};


/**
 * @param {!Array<!proto.CaseLocationGrpcModel>} value
 * @return {!proto.ArrayOfCaseLocationGrpcModel} returns this
*/
proto.ArrayOfCaseLocationGrpcModel.prototype.setElementList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.CaseLocationGrpcModel=} opt_value
 * @param {number=} opt_index
 * @return {!proto.CaseLocationGrpcModel}
 */
proto.ArrayOfCaseLocationGrpcModel.prototype.addElement = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.CaseLocationGrpcModel, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ArrayOfCaseLocationGrpcModel} returns this
 */
proto.ArrayOfCaseLocationGrpcModel.prototype.clearElementList = function() {
  return this.setElementList([]);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ResponseCaseLocation.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ResponseCaseLocation.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  FAILURE: 1,
  CASELOCATION: 2
};

/**
 * @return {proto.ResponseCaseLocation.ResponseCase}
 */
proto.ResponseCaseLocation.prototype.getResponseCase = function() {
  return /** @type {proto.ResponseCaseLocation.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ResponseCaseLocation.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ResponseCaseLocation.prototype.toObject = function(opt_includeInstance) {
  return proto.ResponseCaseLocation.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ResponseCaseLocation} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ResponseCaseLocation.toObject = function(includeInstance, msg) {
  var f, obj = {
failure: (f = msg.getFailure()) && failure_pb.FailureGrpc.toObject(includeInstance, f),
caselocation: (f = msg.getCaselocation()) && proto.CaseLocationGrpcModel.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ResponseCaseLocation}
 */
proto.ResponseCaseLocation.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ResponseCaseLocation;
  return proto.ResponseCaseLocation.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ResponseCaseLocation} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ResponseCaseLocation}
 */
proto.ResponseCaseLocation.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new failure_pb.FailureGrpc;
      reader.readMessage(value,failure_pb.FailureGrpc.deserializeBinaryFromReader);
      msg.setFailure(value);
      break;
    case 2:
      var value = new proto.CaseLocationGrpcModel;
      reader.readMessage(value,proto.CaseLocationGrpcModel.deserializeBinaryFromReader);
      msg.setCaselocation(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ResponseCaseLocation.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ResponseCaseLocation.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ResponseCaseLocation} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ResponseCaseLocation.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFailure();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      failure_pb.FailureGrpc.serializeBinaryToWriter
    );
  }
  f = message.getCaselocation();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.CaseLocationGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * optional FailureGrpc failure = 1;
 * @return {?proto.FailureGrpc}
 */
proto.ResponseCaseLocation.prototype.getFailure = function() {
  return /** @type{?proto.FailureGrpc} */ (
    jspb.Message.getWrapperField(this, failure_pb.FailureGrpc, 1));
};


/**
 * @param {?proto.FailureGrpc|undefined} value
 * @return {!proto.ResponseCaseLocation} returns this
*/
proto.ResponseCaseLocation.prototype.setFailure = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ResponseCaseLocation.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ResponseCaseLocation} returns this
 */
proto.ResponseCaseLocation.prototype.clearFailure = function() {
  return this.setFailure(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ResponseCaseLocation.prototype.hasFailure = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional CaseLocationGrpcModel caseLocation = 2;
 * @return {?proto.CaseLocationGrpcModel}
 */
proto.ResponseCaseLocation.prototype.getCaselocation = function() {
  return /** @type{?proto.CaseLocationGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.CaseLocationGrpcModel, 2));
};


/**
 * @param {?proto.CaseLocationGrpcModel|undefined} value
 * @return {!proto.ResponseCaseLocation} returns this
*/
proto.ResponseCaseLocation.prototype.setCaselocation = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ResponseCaseLocation.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ResponseCaseLocation} returns this
 */
proto.ResponseCaseLocation.prototype.clearCaselocation = function() {
  return this.setCaselocation(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ResponseCaseLocation.prototype.hasCaselocation = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.RelatedCaseGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.RelatedCaseGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.RelatedCaseGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.RelatedCaseGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
caseid: (f = jspb.Message.getField(msg, 1)) == null ? undefined : f,
relatedcaseid: (f = jspb.Message.getField(msg, 2)) == null ? undefined : f,
relation: (f = jspb.Message.getField(msg, 3)) == null ? undefined : f,
createdat: (f = msg.getCreatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
updatedat: (f = msg.getUpdatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.RelatedCaseGrpcModel}
 */
proto.RelatedCaseGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.RelatedCaseGrpcModel;
  return proto.RelatedCaseGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.RelatedCaseGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.RelatedCaseGrpcModel}
 */
proto.RelatedCaseGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setCaseid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setRelatedcaseid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setRelation(value);
      break;
    case 4:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCreatedat(value);
      break;
    case 5:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setUpdatedat(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.RelatedCaseGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.RelatedCaseGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.RelatedCaseGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.RelatedCaseGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getCreatedat();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getUpdatedat();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
};


/**
 * optional string caseId = 1;
 * @return {string}
 */
proto.RelatedCaseGrpcModel.prototype.getCaseid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.RelatedCaseGrpcModel} returns this
 */
proto.RelatedCaseGrpcModel.prototype.setCaseid = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.RelatedCaseGrpcModel} returns this
 */
proto.RelatedCaseGrpcModel.prototype.clearCaseid = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.RelatedCaseGrpcModel.prototype.hasCaseid = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string relatedCaseId = 2;
 * @return {string}
 */
proto.RelatedCaseGrpcModel.prototype.getRelatedcaseid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.RelatedCaseGrpcModel} returns this
 */
proto.RelatedCaseGrpcModel.prototype.setRelatedcaseid = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.RelatedCaseGrpcModel} returns this
 */
proto.RelatedCaseGrpcModel.prototype.clearRelatedcaseid = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.RelatedCaseGrpcModel.prototype.hasRelatedcaseid = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string relation = 3;
 * @return {string}
 */
proto.RelatedCaseGrpcModel.prototype.getRelation = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.RelatedCaseGrpcModel} returns this
 */
proto.RelatedCaseGrpcModel.prototype.setRelation = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.RelatedCaseGrpcModel} returns this
 */
proto.RelatedCaseGrpcModel.prototype.clearRelation = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.RelatedCaseGrpcModel.prototype.hasRelation = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional google.protobuf.Timestamp createdAt = 4;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.RelatedCaseGrpcModel.prototype.getCreatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 4));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.RelatedCaseGrpcModel} returns this
*/
proto.RelatedCaseGrpcModel.prototype.setCreatedat = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.RelatedCaseGrpcModel} returns this
 */
proto.RelatedCaseGrpcModel.prototype.clearCreatedat = function() {
  return this.setCreatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.RelatedCaseGrpcModel.prototype.hasCreatedat = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional google.protobuf.Timestamp updatedAt = 5;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.RelatedCaseGrpcModel.prototype.getUpdatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 5));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.RelatedCaseGrpcModel} returns this
*/
proto.RelatedCaseGrpcModel.prototype.setUpdatedat = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.RelatedCaseGrpcModel} returns this
 */
proto.RelatedCaseGrpcModel.prototype.clearUpdatedat = function() {
  return this.setUpdatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.RelatedCaseGrpcModel.prototype.hasUpdatedat = function() {
  return jspb.Message.getField(this, 5) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ArrayOfRelatedCaseGrpcModel.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ArrayOfRelatedCaseGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.ArrayOfRelatedCaseGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ArrayOfRelatedCaseGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfRelatedCaseGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
elementList: jspb.Message.toObjectList(msg.getElementList(),
    proto.RelatedCaseGrpcModel.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ArrayOfRelatedCaseGrpcModel}
 */
proto.ArrayOfRelatedCaseGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ArrayOfRelatedCaseGrpcModel;
  return proto.ArrayOfRelatedCaseGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ArrayOfRelatedCaseGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ArrayOfRelatedCaseGrpcModel}
 */
proto.ArrayOfRelatedCaseGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.RelatedCaseGrpcModel;
      reader.readMessage(value,proto.RelatedCaseGrpcModel.deserializeBinaryFromReader);
      msg.addElement(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ArrayOfRelatedCaseGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ArrayOfRelatedCaseGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ArrayOfRelatedCaseGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfRelatedCaseGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getElementList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.RelatedCaseGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * repeated RelatedCaseGrpcModel element = 1;
 * @return {!Array<!proto.RelatedCaseGrpcModel>}
 */
proto.ArrayOfRelatedCaseGrpcModel.prototype.getElementList = function() {
  return /** @type{!Array<!proto.RelatedCaseGrpcModel>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.RelatedCaseGrpcModel, 1));
};


/**
 * @param {!Array<!proto.RelatedCaseGrpcModel>} value
 * @return {!proto.ArrayOfRelatedCaseGrpcModel} returns this
*/
proto.ArrayOfRelatedCaseGrpcModel.prototype.setElementList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.RelatedCaseGrpcModel=} opt_value
 * @param {number=} opt_index
 * @return {!proto.RelatedCaseGrpcModel}
 */
proto.ArrayOfRelatedCaseGrpcModel.prototype.addElement = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.RelatedCaseGrpcModel, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ArrayOfRelatedCaseGrpcModel} returns this
 */
proto.ArrayOfRelatedCaseGrpcModel.prototype.clearElementList = function() {
  return this.setElementList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CaseDetailGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.CaseDetailGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CaseDetailGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseDetailGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
id: (f = jspb.Message.getField(msg, 1)) == null ? undefined : f,
caseid: (f = jspb.Message.getField(msg, 2)) == null ? undefined : f,
key: (f = jspb.Message.getField(msg, 3)) == null ? undefined : f,
value: (f = jspb.Message.getField(msg, 4)) == null ? undefined : f,
createdby: (f = jspb.Message.getField(msg, 5)) == null ? undefined : f,
updatedby: (f = jspb.Message.getField(msg, 6)) == null ? undefined : f,
createdat: (f = msg.getCreatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
updatedat: (f = msg.getUpdatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CaseDetailGrpcModel}
 */
proto.CaseDetailGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CaseDetailGrpcModel;
  return proto.CaseDetailGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CaseDetailGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CaseDetailGrpcModel}
 */
proto.CaseDetailGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setCaseid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setKey(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setValue(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setCreatedby(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setUpdatedby(value);
      break;
    case 7:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCreatedat(value);
      break;
    case 8:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setUpdatedat(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CaseDetailGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CaseDetailGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CaseDetailGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseDetailGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeString(
      5,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getCreatedat();
  if (f != null) {
    writer.writeMessage(
      7,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getUpdatedat();
  if (f != null) {
    writer.writeMessage(
      8,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.CaseDetailGrpcModel.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.setId = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.clearId = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseDetailGrpcModel.prototype.hasId = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string caseId = 2;
 * @return {string}
 */
proto.CaseDetailGrpcModel.prototype.getCaseid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.setCaseid = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.clearCaseid = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseDetailGrpcModel.prototype.hasCaseid = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string key = 3;
 * @return {string}
 */
proto.CaseDetailGrpcModel.prototype.getKey = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.setKey = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.clearKey = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseDetailGrpcModel.prototype.hasKey = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string value = 4;
 * @return {string}
 */
proto.CaseDetailGrpcModel.prototype.getValue = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.setValue = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.clearValue = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseDetailGrpcModel.prototype.hasValue = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional string createdBy = 5;
 * @return {string}
 */
proto.CaseDetailGrpcModel.prototype.getCreatedby = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.setCreatedby = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.clearCreatedby = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseDetailGrpcModel.prototype.hasCreatedby = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional string updatedBy = 6;
 * @return {string}
 */
proto.CaseDetailGrpcModel.prototype.getUpdatedby = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.setUpdatedby = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.clearUpdatedby = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseDetailGrpcModel.prototype.hasUpdatedby = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional google.protobuf.Timestamp createdAt = 7;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseDetailGrpcModel.prototype.getCreatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 7));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseDetailGrpcModel} returns this
*/
proto.CaseDetailGrpcModel.prototype.setCreatedat = function(value) {
  return jspb.Message.setWrapperField(this, 7, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.clearCreatedat = function() {
  return this.setCreatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseDetailGrpcModel.prototype.hasCreatedat = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional google.protobuf.Timestamp updatedAt = 8;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseDetailGrpcModel.prototype.getUpdatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 8));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseDetailGrpcModel} returns this
*/
proto.CaseDetailGrpcModel.prototype.setUpdatedat = function(value) {
  return jspb.Message.setWrapperField(this, 8, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseDetailGrpcModel} returns this
 */
proto.CaseDetailGrpcModel.prototype.clearUpdatedat = function() {
  return this.setUpdatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseDetailGrpcModel.prototype.hasUpdatedat = function() {
  return jspb.Message.getField(this, 8) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ArrayOfCaseDetailGrpcModel.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ArrayOfCaseDetailGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.ArrayOfCaseDetailGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ArrayOfCaseDetailGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfCaseDetailGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
elementList: jspb.Message.toObjectList(msg.getElementList(),
    proto.CaseDetailGrpcModel.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ArrayOfCaseDetailGrpcModel}
 */
proto.ArrayOfCaseDetailGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ArrayOfCaseDetailGrpcModel;
  return proto.ArrayOfCaseDetailGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ArrayOfCaseDetailGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ArrayOfCaseDetailGrpcModel}
 */
proto.ArrayOfCaseDetailGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.CaseDetailGrpcModel;
      reader.readMessage(value,proto.CaseDetailGrpcModel.deserializeBinaryFromReader);
      msg.addElement(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ArrayOfCaseDetailGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ArrayOfCaseDetailGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ArrayOfCaseDetailGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfCaseDetailGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getElementList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.CaseDetailGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * repeated CaseDetailGrpcModel element = 1;
 * @return {!Array<!proto.CaseDetailGrpcModel>}
 */
proto.ArrayOfCaseDetailGrpcModel.prototype.getElementList = function() {
  return /** @type{!Array<!proto.CaseDetailGrpcModel>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.CaseDetailGrpcModel, 1));
};


/**
 * @param {!Array<!proto.CaseDetailGrpcModel>} value
 * @return {!proto.ArrayOfCaseDetailGrpcModel} returns this
*/
proto.ArrayOfCaseDetailGrpcModel.prototype.setElementList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.CaseDetailGrpcModel=} opt_value
 * @param {number=} opt_index
 * @return {!proto.CaseDetailGrpcModel}
 */
proto.ArrayOfCaseDetailGrpcModel.prototype.addElement = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.CaseDetailGrpcModel, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ArrayOfCaseDetailGrpcModel} returns this
 */
proto.ArrayOfCaseDetailGrpcModel.prototype.clearElementList = function() {
  return this.setElementList([]);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ResponseRelatedCase.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ResponseRelatedCase.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  FAILURE: 1,
  RELATEDCASE: 2
};

/**
 * @return {proto.ResponseRelatedCase.ResponseCase}
 */
proto.ResponseRelatedCase.prototype.getResponseCase = function() {
  return /** @type {proto.ResponseRelatedCase.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ResponseRelatedCase.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ResponseRelatedCase.prototype.toObject = function(opt_includeInstance) {
  return proto.ResponseRelatedCase.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ResponseRelatedCase} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ResponseRelatedCase.toObject = function(includeInstance, msg) {
  var f, obj = {
failure: (f = msg.getFailure()) && failure_pb.FailureGrpc.toObject(includeInstance, f),
relatedcase: (f = msg.getRelatedcase()) && proto.RelatedCaseGrpcModel.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ResponseRelatedCase}
 */
proto.ResponseRelatedCase.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ResponseRelatedCase;
  return proto.ResponseRelatedCase.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ResponseRelatedCase} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ResponseRelatedCase}
 */
proto.ResponseRelatedCase.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new failure_pb.FailureGrpc;
      reader.readMessage(value,failure_pb.FailureGrpc.deserializeBinaryFromReader);
      msg.setFailure(value);
      break;
    case 2:
      var value = new proto.RelatedCaseGrpcModel;
      reader.readMessage(value,proto.RelatedCaseGrpcModel.deserializeBinaryFromReader);
      msg.setRelatedcase(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ResponseRelatedCase.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ResponseRelatedCase.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ResponseRelatedCase} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ResponseRelatedCase.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFailure();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      failure_pb.FailureGrpc.serializeBinaryToWriter
    );
  }
  f = message.getRelatedcase();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.RelatedCaseGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * optional FailureGrpc failure = 1;
 * @return {?proto.FailureGrpc}
 */
proto.ResponseRelatedCase.prototype.getFailure = function() {
  return /** @type{?proto.FailureGrpc} */ (
    jspb.Message.getWrapperField(this, failure_pb.FailureGrpc, 1));
};


/**
 * @param {?proto.FailureGrpc|undefined} value
 * @return {!proto.ResponseRelatedCase} returns this
*/
proto.ResponseRelatedCase.prototype.setFailure = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ResponseRelatedCase.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ResponseRelatedCase} returns this
 */
proto.ResponseRelatedCase.prototype.clearFailure = function() {
  return this.setFailure(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ResponseRelatedCase.prototype.hasFailure = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional RelatedCaseGrpcModel relatedCase = 2;
 * @return {?proto.RelatedCaseGrpcModel}
 */
proto.ResponseRelatedCase.prototype.getRelatedcase = function() {
  return /** @type{?proto.RelatedCaseGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.RelatedCaseGrpcModel, 2));
};


/**
 * @param {?proto.RelatedCaseGrpcModel|undefined} value
 * @return {!proto.ResponseRelatedCase} returns this
*/
proto.ResponseRelatedCase.prototype.setRelatedcase = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ResponseRelatedCase.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ResponseRelatedCase} returns this
 */
proto.ResponseRelatedCase.prototype.clearRelatedcase = function() {
  return this.setRelatedcase(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ResponseRelatedCase.prototype.hasRelatedcase = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.SubjectRelatedCaseGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.SubjectRelatedCaseGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.SubjectRelatedCaseGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SubjectRelatedCaseGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
caseid: (f = jspb.Message.getField(msg, 1)) == null ? undefined : f,
relatedsubjectid: (f = jspb.Message.getField(msg, 2)) == null ? undefined : f,
relation: (f = jspb.Message.getField(msg, 3)) == null ? undefined : f,
createdat: (f = msg.getCreatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
updatedat: (f = msg.getUpdatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.SubjectRelatedCaseGrpcModel}
 */
proto.SubjectRelatedCaseGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.SubjectRelatedCaseGrpcModel;
  return proto.SubjectRelatedCaseGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.SubjectRelatedCaseGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.SubjectRelatedCaseGrpcModel}
 */
proto.SubjectRelatedCaseGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setCaseid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setRelatedsubjectid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setRelation(value);
      break;
    case 4:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCreatedat(value);
      break;
    case 5:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setUpdatedat(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.SubjectRelatedCaseGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.SubjectRelatedCaseGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.SubjectRelatedCaseGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.SubjectRelatedCaseGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getCreatedat();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getUpdatedat();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
};


/**
 * optional string caseId = 1;
 * @return {string}
 */
proto.SubjectRelatedCaseGrpcModel.prototype.getCaseid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubjectRelatedCaseGrpcModel} returns this
 */
proto.SubjectRelatedCaseGrpcModel.prototype.setCaseid = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectRelatedCaseGrpcModel} returns this
 */
proto.SubjectRelatedCaseGrpcModel.prototype.clearCaseid = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectRelatedCaseGrpcModel.prototype.hasCaseid = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string relatedSubjectId = 2;
 * @return {string}
 */
proto.SubjectRelatedCaseGrpcModel.prototype.getRelatedsubjectid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubjectRelatedCaseGrpcModel} returns this
 */
proto.SubjectRelatedCaseGrpcModel.prototype.setRelatedsubjectid = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectRelatedCaseGrpcModel} returns this
 */
proto.SubjectRelatedCaseGrpcModel.prototype.clearRelatedsubjectid = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectRelatedCaseGrpcModel.prototype.hasRelatedsubjectid = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string relation = 3;
 * @return {string}
 */
proto.SubjectRelatedCaseGrpcModel.prototype.getRelation = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.SubjectRelatedCaseGrpcModel} returns this
 */
proto.SubjectRelatedCaseGrpcModel.prototype.setRelation = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.SubjectRelatedCaseGrpcModel} returns this
 */
proto.SubjectRelatedCaseGrpcModel.prototype.clearRelation = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectRelatedCaseGrpcModel.prototype.hasRelation = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional google.protobuf.Timestamp createdAt = 4;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.SubjectRelatedCaseGrpcModel.prototype.getCreatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 4));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.SubjectRelatedCaseGrpcModel} returns this
*/
proto.SubjectRelatedCaseGrpcModel.prototype.setCreatedat = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.SubjectRelatedCaseGrpcModel} returns this
 */
proto.SubjectRelatedCaseGrpcModel.prototype.clearCreatedat = function() {
  return this.setCreatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectRelatedCaseGrpcModel.prototype.hasCreatedat = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional google.protobuf.Timestamp updatedAt = 5;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.SubjectRelatedCaseGrpcModel.prototype.getUpdatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 5));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.SubjectRelatedCaseGrpcModel} returns this
*/
proto.SubjectRelatedCaseGrpcModel.prototype.setUpdatedat = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.SubjectRelatedCaseGrpcModel} returns this
 */
proto.SubjectRelatedCaseGrpcModel.prototype.clearUpdatedat = function() {
  return this.setUpdatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.SubjectRelatedCaseGrpcModel.prototype.hasUpdatedat = function() {
  return jspb.Message.getField(this, 5) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.ArrayOfSubjectRelatedCaseGrpcModel.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ArrayOfSubjectRelatedCaseGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.ArrayOfSubjectRelatedCaseGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ArrayOfSubjectRelatedCaseGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfSubjectRelatedCaseGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
elementList: jspb.Message.toObjectList(msg.getElementList(),
    proto.SubjectRelatedCaseGrpcModel.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ArrayOfSubjectRelatedCaseGrpcModel}
 */
proto.ArrayOfSubjectRelatedCaseGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ArrayOfSubjectRelatedCaseGrpcModel;
  return proto.ArrayOfSubjectRelatedCaseGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ArrayOfSubjectRelatedCaseGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ArrayOfSubjectRelatedCaseGrpcModel}
 */
proto.ArrayOfSubjectRelatedCaseGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.SubjectRelatedCaseGrpcModel;
      reader.readMessage(value,proto.SubjectRelatedCaseGrpcModel.deserializeBinaryFromReader);
      msg.addElement(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ArrayOfSubjectRelatedCaseGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ArrayOfSubjectRelatedCaseGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ArrayOfSubjectRelatedCaseGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ArrayOfSubjectRelatedCaseGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getElementList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.SubjectRelatedCaseGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * repeated SubjectRelatedCaseGrpcModel element = 1;
 * @return {!Array<!proto.SubjectRelatedCaseGrpcModel>}
 */
proto.ArrayOfSubjectRelatedCaseGrpcModel.prototype.getElementList = function() {
  return /** @type{!Array<!proto.SubjectRelatedCaseGrpcModel>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.SubjectRelatedCaseGrpcModel, 1));
};


/**
 * @param {!Array<!proto.SubjectRelatedCaseGrpcModel>} value
 * @return {!proto.ArrayOfSubjectRelatedCaseGrpcModel} returns this
*/
proto.ArrayOfSubjectRelatedCaseGrpcModel.prototype.setElementList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.SubjectRelatedCaseGrpcModel=} opt_value
 * @param {number=} opt_index
 * @return {!proto.SubjectRelatedCaseGrpcModel}
 */
proto.ArrayOfSubjectRelatedCaseGrpcModel.prototype.addElement = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.SubjectRelatedCaseGrpcModel, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.ArrayOfSubjectRelatedCaseGrpcModel} returns this
 */
proto.ArrayOfSubjectRelatedCaseGrpcModel.prototype.clearElementList = function() {
  return this.setElementList([]);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.ResponseSubjectRelatedCase.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.ResponseSubjectRelatedCase.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  FAILURE: 1,
  SUBJECTRELATEDCASE: 2
};

/**
 * @return {proto.ResponseSubjectRelatedCase.ResponseCase}
 */
proto.ResponseSubjectRelatedCase.prototype.getResponseCase = function() {
  return /** @type {proto.ResponseSubjectRelatedCase.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.ResponseSubjectRelatedCase.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.ResponseSubjectRelatedCase.prototype.toObject = function(opt_includeInstance) {
  return proto.ResponseSubjectRelatedCase.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.ResponseSubjectRelatedCase} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ResponseSubjectRelatedCase.toObject = function(includeInstance, msg) {
  var f, obj = {
failure: (f = msg.getFailure()) && failure_pb.FailureGrpc.toObject(includeInstance, f),
subjectrelatedcase: (f = msg.getSubjectrelatedcase()) && proto.SubjectRelatedCaseGrpcModel.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.ResponseSubjectRelatedCase}
 */
proto.ResponseSubjectRelatedCase.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.ResponseSubjectRelatedCase;
  return proto.ResponseSubjectRelatedCase.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.ResponseSubjectRelatedCase} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.ResponseSubjectRelatedCase}
 */
proto.ResponseSubjectRelatedCase.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new failure_pb.FailureGrpc;
      reader.readMessage(value,failure_pb.FailureGrpc.deserializeBinaryFromReader);
      msg.setFailure(value);
      break;
    case 2:
      var value = new proto.SubjectRelatedCaseGrpcModel;
      reader.readMessage(value,proto.SubjectRelatedCaseGrpcModel.deserializeBinaryFromReader);
      msg.setSubjectrelatedcase(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.ResponseSubjectRelatedCase.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.ResponseSubjectRelatedCase.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.ResponseSubjectRelatedCase} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.ResponseSubjectRelatedCase.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFailure();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      failure_pb.FailureGrpc.serializeBinaryToWriter
    );
  }
  f = message.getSubjectrelatedcase();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.SubjectRelatedCaseGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * optional FailureGrpc failure = 1;
 * @return {?proto.FailureGrpc}
 */
proto.ResponseSubjectRelatedCase.prototype.getFailure = function() {
  return /** @type{?proto.FailureGrpc} */ (
    jspb.Message.getWrapperField(this, failure_pb.FailureGrpc, 1));
};


/**
 * @param {?proto.FailureGrpc|undefined} value
 * @return {!proto.ResponseSubjectRelatedCase} returns this
*/
proto.ResponseSubjectRelatedCase.prototype.setFailure = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.ResponseSubjectRelatedCase.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ResponseSubjectRelatedCase} returns this
 */
proto.ResponseSubjectRelatedCase.prototype.clearFailure = function() {
  return this.setFailure(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ResponseSubjectRelatedCase.prototype.hasFailure = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional SubjectRelatedCaseGrpcModel subjectRelatedCase = 2;
 * @return {?proto.SubjectRelatedCaseGrpcModel}
 */
proto.ResponseSubjectRelatedCase.prototype.getSubjectrelatedcase = function() {
  return /** @type{?proto.SubjectRelatedCaseGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.SubjectRelatedCaseGrpcModel, 2));
};


/**
 * @param {?proto.SubjectRelatedCaseGrpcModel|undefined} value
 * @return {!proto.ResponseSubjectRelatedCase} returns this
*/
proto.ResponseSubjectRelatedCase.prototype.setSubjectrelatedcase = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.ResponseSubjectRelatedCase.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.ResponseSubjectRelatedCase} returns this
 */
proto.ResponseSubjectRelatedCase.prototype.clearSubjectrelatedcase = function() {
  return this.setSubjectrelatedcase(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.ResponseSubjectRelatedCase.prototype.hasSubjectrelatedcase = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CaseEvidenceGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.CaseEvidenceGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CaseEvidenceGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseEvidenceGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
id: (f = jspb.Message.getField(msg, 1)) == null ? undefined : f,
caseid: (f = jspb.Message.getField(msg, 2)) == null ? undefined : f,
rawid: (f = jspb.Message.getField(msg, 3)) == null ? undefined : f,
name: (f = jspb.Message.getField(msg, 4)) == null ? undefined : f,
type: (f = jspb.Message.getField(msg, 5)) == null ? undefined : f,
location: (f = jspb.Message.getField(msg, 6)) == null ? undefined : f,
status: (f = jspb.Message.getField(msg, 7)) == null ? undefined : f,
content: msg.getContent_asB64(),
mimetype: (f = jspb.Message.getField(msg, 9)) == null ? undefined : f,
obtainedat: (f = msg.getObtainedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
createdat: (f = msg.getCreatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
updatedat: (f = msg.getUpdatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CaseEvidenceGrpcModel}
 */
proto.CaseEvidenceGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CaseEvidenceGrpcModel;
  return proto.CaseEvidenceGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CaseEvidenceGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CaseEvidenceGrpcModel}
 */
proto.CaseEvidenceGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setCaseid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setRawid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setType(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setLocation(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setStatus(value);
      break;
    case 8:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setContent(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setMimetype(value);
      break;
    case 10:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setObtainedat(value);
      break;
    case 11:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCreatedat(value);
      break;
    case 12:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setUpdatedat(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CaseEvidenceGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CaseEvidenceGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CaseEvidenceGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseEvidenceGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeString(
      5,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeString(
      6,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 7));
  if (f != null) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getContent_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      8,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 9));
  if (f != null) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getObtainedat();
  if (f != null) {
    writer.writeMessage(
      10,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getCreatedat();
  if (f != null) {
    writer.writeMessage(
      11,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getUpdatedat();
  if (f != null) {
    writer.writeMessage(
      12,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.CaseEvidenceGrpcModel.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.setId = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.clearId = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseEvidenceGrpcModel.prototype.hasId = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string caseId = 2;
 * @return {string}
 */
proto.CaseEvidenceGrpcModel.prototype.getCaseid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.setCaseid = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.clearCaseid = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseEvidenceGrpcModel.prototype.hasCaseid = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string rawId = 3;
 * @return {string}
 */
proto.CaseEvidenceGrpcModel.prototype.getRawid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.setRawid = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.clearRawid = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseEvidenceGrpcModel.prototype.hasRawid = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string name = 4;
 * @return {string}
 */
proto.CaseEvidenceGrpcModel.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.setName = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.clearName = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseEvidenceGrpcModel.prototype.hasName = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional string type = 5;
 * @return {string}
 */
proto.CaseEvidenceGrpcModel.prototype.getType = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.setType = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.clearType = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseEvidenceGrpcModel.prototype.hasType = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional string location = 6;
 * @return {string}
 */
proto.CaseEvidenceGrpcModel.prototype.getLocation = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.setLocation = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.clearLocation = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseEvidenceGrpcModel.prototype.hasLocation = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional string status = 7;
 * @return {string}
 */
proto.CaseEvidenceGrpcModel.prototype.getStatus = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.setStatus = function(value) {
  return jspb.Message.setField(this, 7, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.clearStatus = function() {
  return jspb.Message.setField(this, 7, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseEvidenceGrpcModel.prototype.hasStatus = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional bytes content = 8;
 * @return {string}
 */
proto.CaseEvidenceGrpcModel.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * optional bytes content = 8;
 * This is a type-conversion wrapper around `getContent()`
 * @return {string}
 */
proto.CaseEvidenceGrpcModel.prototype.getContent_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getContent()));
};


/**
 * optional bytes content = 8;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getContent()`
 * @return {!Uint8Array}
 */
proto.CaseEvidenceGrpcModel.prototype.getContent_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getContent()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.setContent = function(value) {
  return jspb.Message.setProto3BytesField(this, 8, value);
};


/**
 * optional string mimeType = 9;
 * @return {string}
 */
proto.CaseEvidenceGrpcModel.prototype.getMimetype = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.setMimetype = function(value) {
  return jspb.Message.setField(this, 9, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.clearMimetype = function() {
  return jspb.Message.setField(this, 9, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseEvidenceGrpcModel.prototype.hasMimetype = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional google.protobuf.Timestamp obtainedAt = 10;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseEvidenceGrpcModel.prototype.getObtainedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 10));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseEvidenceGrpcModel} returns this
*/
proto.CaseEvidenceGrpcModel.prototype.setObtainedat = function(value) {
  return jspb.Message.setWrapperField(this, 10, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.clearObtainedat = function() {
  return this.setObtainedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseEvidenceGrpcModel.prototype.hasObtainedat = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional google.protobuf.Timestamp createdAt = 11;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseEvidenceGrpcModel.prototype.getCreatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 11));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseEvidenceGrpcModel} returns this
*/
proto.CaseEvidenceGrpcModel.prototype.setCreatedat = function(value) {
  return jspb.Message.setWrapperField(this, 11, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.clearCreatedat = function() {
  return this.setCreatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseEvidenceGrpcModel.prototype.hasCreatedat = function() {
  return jspb.Message.getField(this, 11) != null;
};


/**
 * optional google.protobuf.Timestamp updatedAt = 12;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseEvidenceGrpcModel.prototype.getUpdatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 12));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseEvidenceGrpcModel} returns this
*/
proto.CaseEvidenceGrpcModel.prototype.setUpdatedat = function(value) {
  return jspb.Message.setWrapperField(this, 12, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseEvidenceGrpcModel} returns this
 */
proto.CaseEvidenceGrpcModel.prototype.clearUpdatedat = function() {
  return this.setUpdatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseEvidenceGrpcModel.prototype.hasUpdatedat = function() {
  return jspb.Message.getField(this, 12) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CaseCommentGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.CaseCommentGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CaseCommentGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseCommentGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
id: (f = jspb.Message.getField(msg, 1)) == null ? undefined : f,
caseid: (f = jspb.Message.getField(msg, 2)) == null ? undefined : f,
comment: (f = jspb.Message.getField(msg, 3)) == null ? undefined : f,
createdby: (f = jspb.Message.getField(msg, 4)) == null ? undefined : f,
createdat: (f = msg.getCreatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CaseCommentGrpcModel}
 */
proto.CaseCommentGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CaseCommentGrpcModel;
  return proto.CaseCommentGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CaseCommentGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CaseCommentGrpcModel}
 */
proto.CaseCommentGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setCaseid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setComment(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setCreatedby(value);
      break;
    case 5:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCreatedat(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CaseCommentGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CaseCommentGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CaseCommentGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseCommentGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getCreatedat();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.CaseCommentGrpcModel.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCommentGrpcModel} returns this
 */
proto.CaseCommentGrpcModel.prototype.setId = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCommentGrpcModel} returns this
 */
proto.CaseCommentGrpcModel.prototype.clearId = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCommentGrpcModel.prototype.hasId = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string caseId = 2;
 * @return {string}
 */
proto.CaseCommentGrpcModel.prototype.getCaseid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCommentGrpcModel} returns this
 */
proto.CaseCommentGrpcModel.prototype.setCaseid = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCommentGrpcModel} returns this
 */
proto.CaseCommentGrpcModel.prototype.clearCaseid = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCommentGrpcModel.prototype.hasCaseid = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string comment = 3;
 * @return {string}
 */
proto.CaseCommentGrpcModel.prototype.getComment = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCommentGrpcModel} returns this
 */
proto.CaseCommentGrpcModel.prototype.setComment = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCommentGrpcModel} returns this
 */
proto.CaseCommentGrpcModel.prototype.clearComment = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCommentGrpcModel.prototype.hasComment = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string createdBy = 4;
 * @return {string}
 */
proto.CaseCommentGrpcModel.prototype.getCreatedby = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCommentGrpcModel} returns this
 */
proto.CaseCommentGrpcModel.prototype.setCreatedby = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCommentGrpcModel} returns this
 */
proto.CaseCommentGrpcModel.prototype.clearCreatedby = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCommentGrpcModel.prototype.hasCreatedby = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional google.protobuf.Timestamp createdAt = 5;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseCommentGrpcModel.prototype.getCreatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 5));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseCommentGrpcModel} returns this
*/
proto.CaseCommentGrpcModel.prototype.setCreatedat = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseCommentGrpcModel} returns this
 */
proto.CaseCommentGrpcModel.prototype.clearCreatedat = function() {
  return this.setCreatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCommentGrpcModel.prototype.hasCreatedat = function() {
  return jspb.Message.getField(this, 5) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CaseCoincidenceGrpcModel.prototype.toObject = function(opt_includeInstance) {
  return proto.CaseCoincidenceGrpcModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CaseCoincidenceGrpcModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseCoincidenceGrpcModel.toObject = function(includeInstance, msg) {
  var f, obj = {
id: (f = jspb.Message.getField(msg, 1)) == null ? undefined : f,
caseid: (f = jspb.Message.getField(msg, 2)) == null ? undefined : f,
evidenceid: (f = jspb.Message.getField(msg, 3)) == null ? undefined : f,
coincidencesubjectid: (f = jspb.Message.getField(msg, 4)) == null ? undefined : f,
type: (f = jspb.Message.getField(msg, 5)) == null ? undefined : f,
status: (f = jspb.Message.getField(msg, 6)) == null ? undefined : f,
score: (f = jspb.Message.getField(msg, 7)) == null ? undefined : f,
rawid: (f = jspb.Message.getField(msg, 8)) == null ? undefined : f,
searchfilters: (f = jspb.Message.getField(msg, 9)) == null ? undefined : f,
authuserid: (f = jspb.Message.getField(msg, 10)) == null ? undefined : f,
authusernumid: (f = jspb.Message.getField(msg, 11)) == null ? undefined : f,
authusersignaturetech: (f = jspb.Message.getField(msg, 12)) == null ? undefined : f,
authusersignaturedate: (f = msg.getAuthusersignaturedate()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
content: msg.getContent_asB64(),
mimetype: (f = jspb.Message.getField(msg, 15)) == null ? undefined : f,
createdby: (f = jspb.Message.getField(msg, 16)) == null ? undefined : f,
updatedby: (f = jspb.Message.getField(msg, 17)) == null ? undefined : f,
createdat: (f = msg.getCreatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
updatedat: (f = msg.getUpdatedat()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CaseCoincidenceGrpcModel}
 */
proto.CaseCoincidenceGrpcModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CaseCoincidenceGrpcModel;
  return proto.CaseCoincidenceGrpcModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CaseCoincidenceGrpcModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CaseCoincidenceGrpcModel}
 */
proto.CaseCoincidenceGrpcModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setCaseid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setEvidenceid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setCoincidencesubjectid(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setType(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setStatus(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setScore(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setRawid(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setSearchfilters(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setAuthuserid(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setAuthusernumid(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setAuthusersignaturetech(value);
      break;
    case 13:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setAuthusersignaturedate(value);
      break;
    case 14:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setContent(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setMimetype(value);
      break;
    case 16:
      var value = /** @type {string} */ (reader.readString());
      msg.setCreatedby(value);
      break;
    case 17:
      var value = /** @type {string} */ (reader.readString());
      msg.setUpdatedby(value);
      break;
    case 18:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setCreatedat(value);
      break;
    case 19:
      var value = new google_protobuf_timestamp_pb.Timestamp;
      reader.readMessage(value,google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
      msg.setUpdatedat(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CaseCoincidenceGrpcModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CaseCoincidenceGrpcModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CaseCoincidenceGrpcModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CaseCoincidenceGrpcModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeString(
      4,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeString(
      5,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeString(
      6,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 7));
  if (f != null) {
    writer.writeInt32(
      7,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 8));
  if (f != null) {
    writer.writeString(
      8,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 9));
  if (f != null) {
    writer.writeString(
      9,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 10));
  if (f != null) {
    writer.writeString(
      10,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 11));
  if (f != null) {
    writer.writeString(
      11,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 12));
  if (f != null) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getAuthusersignaturedate();
  if (f != null) {
    writer.writeMessage(
      13,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getContent_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      14,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 15));
  if (f != null) {
    writer.writeString(
      15,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 16));
  if (f != null) {
    writer.writeString(
      16,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 17));
  if (f != null) {
    writer.writeString(
      17,
      f
    );
  }
  f = message.getCreatedat();
  if (f != null) {
    writer.writeMessage(
      18,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
  f = message.getUpdatedat();
  if (f != null) {
    writer.writeMessage(
      19,
      f,
      google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter
    );
  }
};


/**
 * optional string id = 1;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setId = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearId = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasId = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string caseId = 2;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getCaseid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setCaseid = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearCaseid = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasCaseid = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string evidenceId = 3;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getEvidenceid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setEvidenceid = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearEvidenceid = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasEvidenceid = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string coincidenceSubjectId = 4;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getCoincidencesubjectid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setCoincidencesubjectid = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearCoincidencesubjectid = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasCoincidencesubjectid = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional string type = 5;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getType = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setType = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearType = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasType = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional string status = 6;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getStatus = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setStatus = function(value) {
  return jspb.Message.setField(this, 6, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearStatus = function() {
  return jspb.Message.setField(this, 6, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasStatus = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional int32 score = 7;
 * @return {number}
 */
proto.CaseCoincidenceGrpcModel.prototype.getScore = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setScore = function(value) {
  return jspb.Message.setField(this, 7, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearScore = function() {
  return jspb.Message.setField(this, 7, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasScore = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional string rawId = 8;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getRawid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setRawid = function(value) {
  return jspb.Message.setField(this, 8, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearRawid = function() {
  return jspb.Message.setField(this, 8, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasRawid = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional string searchFilters = 9;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getSearchfilters = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setSearchfilters = function(value) {
  return jspb.Message.setField(this, 9, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearSearchfilters = function() {
  return jspb.Message.setField(this, 9, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasSearchfilters = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional string authUserId = 10;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getAuthuserid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setAuthuserid = function(value) {
  return jspb.Message.setField(this, 10, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearAuthuserid = function() {
  return jspb.Message.setField(this, 10, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasAuthuserid = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional string authUserNumId = 11;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getAuthusernumid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setAuthusernumid = function(value) {
  return jspb.Message.setField(this, 11, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearAuthusernumid = function() {
  return jspb.Message.setField(this, 11, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasAuthusernumid = function() {
  return jspb.Message.getField(this, 11) != null;
};


/**
 * optional string authUserSignatureTech = 12;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getAuthusersignaturetech = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setAuthusersignaturetech = function(value) {
  return jspb.Message.setField(this, 12, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearAuthusersignaturetech = function() {
  return jspb.Message.setField(this, 12, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasAuthusersignaturetech = function() {
  return jspb.Message.getField(this, 12) != null;
};


/**
 * optional google.protobuf.Timestamp authUserSignatureDate = 13;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseCoincidenceGrpcModel.prototype.getAuthusersignaturedate = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 13));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
*/
proto.CaseCoincidenceGrpcModel.prototype.setAuthusersignaturedate = function(value) {
  return jspb.Message.setWrapperField(this, 13, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearAuthusersignaturedate = function() {
  return this.setAuthusersignaturedate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasAuthusersignaturedate = function() {
  return jspb.Message.getField(this, 13) != null;
};


/**
 * optional bytes content = 14;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/**
 * optional bytes content = 14;
 * This is a type-conversion wrapper around `getContent()`
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getContent_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getContent()));
};


/**
 * optional bytes content = 14;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getContent()`
 * @return {!Uint8Array}
 */
proto.CaseCoincidenceGrpcModel.prototype.getContent_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getContent()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setContent = function(value) {
  return jspb.Message.setProto3BytesField(this, 14, value);
};


/**
 * optional string mimeType = 15;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getMimetype = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setMimetype = function(value) {
  return jspb.Message.setField(this, 15, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearMimetype = function() {
  return jspb.Message.setField(this, 15, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasMimetype = function() {
  return jspb.Message.getField(this, 15) != null;
};


/**
 * optional string createdBy = 16;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getCreatedby = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 16, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setCreatedby = function(value) {
  return jspb.Message.setField(this, 16, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearCreatedby = function() {
  return jspb.Message.setField(this, 16, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasCreatedby = function() {
  return jspb.Message.getField(this, 16) != null;
};


/**
 * optional string updatedBy = 17;
 * @return {string}
 */
proto.CaseCoincidenceGrpcModel.prototype.getUpdatedby = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 17, ""));
};


/**
 * @param {string} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.setUpdatedby = function(value) {
  return jspb.Message.setField(this, 17, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearUpdatedby = function() {
  return jspb.Message.setField(this, 17, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasUpdatedby = function() {
  return jspb.Message.getField(this, 17) != null;
};


/**
 * optional google.protobuf.Timestamp createdAt = 18;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseCoincidenceGrpcModel.prototype.getCreatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 18));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
*/
proto.CaseCoincidenceGrpcModel.prototype.setCreatedat = function(value) {
  return jspb.Message.setWrapperField(this, 18, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearCreatedat = function() {
  return this.setCreatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasCreatedat = function() {
  return jspb.Message.getField(this, 18) != null;
};


/**
 * optional google.protobuf.Timestamp updatedAt = 19;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.CaseCoincidenceGrpcModel.prototype.getUpdatedat = function() {
  return /** @type{?proto.google.protobuf.Timestamp} */ (
    jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 19));
};


/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
*/
proto.CaseCoincidenceGrpcModel.prototype.setUpdatedat = function(value) {
  return jspb.Message.setWrapperField(this, 19, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CaseCoincidenceGrpcModel} returns this
 */
proto.CaseCoincidenceGrpcModel.prototype.clearUpdatedat = function() {
  return this.setUpdatedat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CaseCoincidenceGrpcModel.prototype.hasUpdatedat = function() {
  return jspb.Message.getField(this, 19) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.CriminalisticCaseCommentResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.CriminalisticCaseCommentResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  FAILURE: 1,
  CASECOMMENT: 2
};

/**
 * @return {proto.CriminalisticCaseCommentResponse.ResponseCase}
 */
proto.CriminalisticCaseCommentResponse.prototype.getResponseCase = function() {
  return /** @type {proto.CriminalisticCaseCommentResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.CriminalisticCaseCommentResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CriminalisticCaseCommentResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.CriminalisticCaseCommentResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CriminalisticCaseCommentResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticCaseCommentResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
failure: (f = msg.getFailure()) && failure_pb.FailureGrpc.toObject(includeInstance, f),
casecomment: (f = msg.getCasecomment()) && proto.CaseCommentGrpcModel.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CriminalisticCaseCommentResponse}
 */
proto.CriminalisticCaseCommentResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CriminalisticCaseCommentResponse;
  return proto.CriminalisticCaseCommentResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CriminalisticCaseCommentResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CriminalisticCaseCommentResponse}
 */
proto.CriminalisticCaseCommentResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new failure_pb.FailureGrpc;
      reader.readMessage(value,failure_pb.FailureGrpc.deserializeBinaryFromReader);
      msg.setFailure(value);
      break;
    case 2:
      var value = new proto.CaseCommentGrpcModel;
      reader.readMessage(value,proto.CaseCommentGrpcModel.deserializeBinaryFromReader);
      msg.setCasecomment(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CriminalisticCaseCommentResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CriminalisticCaseCommentResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CriminalisticCaseCommentResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticCaseCommentResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFailure();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      failure_pb.FailureGrpc.serializeBinaryToWriter
    );
  }
  f = message.getCasecomment();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.CaseCommentGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * optional FailureGrpc failure = 1;
 * @return {?proto.FailureGrpc}
 */
proto.CriminalisticCaseCommentResponse.prototype.getFailure = function() {
  return /** @type{?proto.FailureGrpc} */ (
    jspb.Message.getWrapperField(this, failure_pb.FailureGrpc, 1));
};


/**
 * @param {?proto.FailureGrpc|undefined} value
 * @return {!proto.CriminalisticCaseCommentResponse} returns this
*/
proto.CriminalisticCaseCommentResponse.prototype.setFailure = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.CriminalisticCaseCommentResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticCaseCommentResponse} returns this
 */
proto.CriminalisticCaseCommentResponse.prototype.clearFailure = function() {
  return this.setFailure(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticCaseCommentResponse.prototype.hasFailure = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional CaseCommentGrpcModel caseComment = 2;
 * @return {?proto.CaseCommentGrpcModel}
 */
proto.CriminalisticCaseCommentResponse.prototype.getCasecomment = function() {
  return /** @type{?proto.CaseCommentGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.CaseCommentGrpcModel, 2));
};


/**
 * @param {?proto.CaseCommentGrpcModel|undefined} value
 * @return {!proto.CriminalisticCaseCommentResponse} returns this
*/
proto.CriminalisticCaseCommentResponse.prototype.setCasecomment = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.CriminalisticCaseCommentResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticCaseCommentResponse} returns this
 */
proto.CriminalisticCaseCommentResponse.prototype.clearCasecomment = function() {
  return this.setCasecomment(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticCaseCommentResponse.prototype.hasCasecomment = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.CriminalisticCaseEvidenceResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.CriminalisticCaseEvidenceResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  FAILURE: 1,
  CASEEVIDENCE: 2
};

/**
 * @return {proto.CriminalisticCaseEvidenceResponse.ResponseCase}
 */
proto.CriminalisticCaseEvidenceResponse.prototype.getResponseCase = function() {
  return /** @type {proto.CriminalisticCaseEvidenceResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.CriminalisticCaseEvidenceResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CriminalisticCaseEvidenceResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.CriminalisticCaseEvidenceResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CriminalisticCaseEvidenceResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticCaseEvidenceResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
failure: (f = msg.getFailure()) && failure_pb.FailureGrpc.toObject(includeInstance, f),
caseevidence: (f = msg.getCaseevidence()) && proto.CaseEvidenceGrpcModel.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CriminalisticCaseEvidenceResponse}
 */
proto.CriminalisticCaseEvidenceResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CriminalisticCaseEvidenceResponse;
  return proto.CriminalisticCaseEvidenceResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CriminalisticCaseEvidenceResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CriminalisticCaseEvidenceResponse}
 */
proto.CriminalisticCaseEvidenceResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new failure_pb.FailureGrpc;
      reader.readMessage(value,failure_pb.FailureGrpc.deserializeBinaryFromReader);
      msg.setFailure(value);
      break;
    case 2:
      var value = new proto.CaseEvidenceGrpcModel;
      reader.readMessage(value,proto.CaseEvidenceGrpcModel.deserializeBinaryFromReader);
      msg.setCaseevidence(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CriminalisticCaseEvidenceResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CriminalisticCaseEvidenceResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CriminalisticCaseEvidenceResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticCaseEvidenceResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFailure();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      failure_pb.FailureGrpc.serializeBinaryToWriter
    );
  }
  f = message.getCaseevidence();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.CaseEvidenceGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * optional FailureGrpc failure = 1;
 * @return {?proto.FailureGrpc}
 */
proto.CriminalisticCaseEvidenceResponse.prototype.getFailure = function() {
  return /** @type{?proto.FailureGrpc} */ (
    jspb.Message.getWrapperField(this, failure_pb.FailureGrpc, 1));
};


/**
 * @param {?proto.FailureGrpc|undefined} value
 * @return {!proto.CriminalisticCaseEvidenceResponse} returns this
*/
proto.CriminalisticCaseEvidenceResponse.prototype.setFailure = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.CriminalisticCaseEvidenceResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticCaseEvidenceResponse} returns this
 */
proto.CriminalisticCaseEvidenceResponse.prototype.clearFailure = function() {
  return this.setFailure(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticCaseEvidenceResponse.prototype.hasFailure = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional CaseEvidenceGrpcModel caseEvidence = 2;
 * @return {?proto.CaseEvidenceGrpcModel}
 */
proto.CriminalisticCaseEvidenceResponse.prototype.getCaseevidence = function() {
  return /** @type{?proto.CaseEvidenceGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.CaseEvidenceGrpcModel, 2));
};


/**
 * @param {?proto.CaseEvidenceGrpcModel|undefined} value
 * @return {!proto.CriminalisticCaseEvidenceResponse} returns this
*/
proto.CriminalisticCaseEvidenceResponse.prototype.setCaseevidence = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.CriminalisticCaseEvidenceResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticCaseEvidenceResponse} returns this
 */
proto.CriminalisticCaseEvidenceResponse.prototype.clearCaseevidence = function() {
  return this.setCaseevidence(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticCaseEvidenceResponse.prototype.hasCaseevidence = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.CriminalisticCaseDetailResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.CriminalisticCaseDetailResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  FAILURE: 1,
  CASEDETAILS: 2
};

/**
 * @return {proto.CriminalisticCaseDetailResponse.ResponseCase}
 */
proto.CriminalisticCaseDetailResponse.prototype.getResponseCase = function() {
  return /** @type {proto.CriminalisticCaseDetailResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.CriminalisticCaseDetailResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CriminalisticCaseDetailResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.CriminalisticCaseDetailResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CriminalisticCaseDetailResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticCaseDetailResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
failure: (f = msg.getFailure()) && failure_pb.FailureGrpc.toObject(includeInstance, f),
casedetails: (f = msg.getCasedetails()) && proto.ArrayOfCaseDetailGrpcModel.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CriminalisticCaseDetailResponse}
 */
proto.CriminalisticCaseDetailResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CriminalisticCaseDetailResponse;
  return proto.CriminalisticCaseDetailResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CriminalisticCaseDetailResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CriminalisticCaseDetailResponse}
 */
proto.CriminalisticCaseDetailResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new failure_pb.FailureGrpc;
      reader.readMessage(value,failure_pb.FailureGrpc.deserializeBinaryFromReader);
      msg.setFailure(value);
      break;
    case 2:
      var value = new proto.ArrayOfCaseDetailGrpcModel;
      reader.readMessage(value,proto.ArrayOfCaseDetailGrpcModel.deserializeBinaryFromReader);
      msg.setCasedetails(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CriminalisticCaseDetailResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CriminalisticCaseDetailResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CriminalisticCaseDetailResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticCaseDetailResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFailure();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      failure_pb.FailureGrpc.serializeBinaryToWriter
    );
  }
  f = message.getCasedetails();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ArrayOfCaseDetailGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * optional FailureGrpc failure = 1;
 * @return {?proto.FailureGrpc}
 */
proto.CriminalisticCaseDetailResponse.prototype.getFailure = function() {
  return /** @type{?proto.FailureGrpc} */ (
    jspb.Message.getWrapperField(this, failure_pb.FailureGrpc, 1));
};


/**
 * @param {?proto.FailureGrpc|undefined} value
 * @return {!proto.CriminalisticCaseDetailResponse} returns this
*/
proto.CriminalisticCaseDetailResponse.prototype.setFailure = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.CriminalisticCaseDetailResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticCaseDetailResponse} returns this
 */
proto.CriminalisticCaseDetailResponse.prototype.clearFailure = function() {
  return this.setFailure(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticCaseDetailResponse.prototype.hasFailure = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional ArrayOfCaseDetailGrpcModel caseDetails = 2;
 * @return {?proto.ArrayOfCaseDetailGrpcModel}
 */
proto.CriminalisticCaseDetailResponse.prototype.getCasedetails = function() {
  return /** @type{?proto.ArrayOfCaseDetailGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.ArrayOfCaseDetailGrpcModel, 2));
};


/**
 * @param {?proto.ArrayOfCaseDetailGrpcModel|undefined} value
 * @return {!proto.CriminalisticCaseDetailResponse} returns this
*/
proto.CriminalisticCaseDetailResponse.prototype.setCasedetails = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.CriminalisticCaseDetailResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticCaseDetailResponse} returns this
 */
proto.CriminalisticCaseDetailResponse.prototype.clearCasedetails = function() {
  return this.setCasedetails(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticCaseDetailResponse.prototype.hasCasedetails = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.CriminalisticRelatedCasesResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.CriminalisticRelatedCasesResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  FAILURE: 1,
  RELATEDCASES: 2
};

/**
 * @return {proto.CriminalisticRelatedCasesResponse.ResponseCase}
 */
proto.CriminalisticRelatedCasesResponse.prototype.getResponseCase = function() {
  return /** @type {proto.CriminalisticRelatedCasesResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.CriminalisticRelatedCasesResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CriminalisticRelatedCasesResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.CriminalisticRelatedCasesResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CriminalisticRelatedCasesResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticRelatedCasesResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
failure: (f = msg.getFailure()) && failure_pb.FailureGrpc.toObject(includeInstance, f),
relatedcases: (f = msg.getRelatedcases()) && proto.ArrayOfRelatedCaseGrpcModel.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CriminalisticRelatedCasesResponse}
 */
proto.CriminalisticRelatedCasesResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CriminalisticRelatedCasesResponse;
  return proto.CriminalisticRelatedCasesResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CriminalisticRelatedCasesResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CriminalisticRelatedCasesResponse}
 */
proto.CriminalisticRelatedCasesResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new failure_pb.FailureGrpc;
      reader.readMessage(value,failure_pb.FailureGrpc.deserializeBinaryFromReader);
      msg.setFailure(value);
      break;
    case 2:
      var value = new proto.ArrayOfRelatedCaseGrpcModel;
      reader.readMessage(value,proto.ArrayOfRelatedCaseGrpcModel.deserializeBinaryFromReader);
      msg.setRelatedcases(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CriminalisticRelatedCasesResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CriminalisticRelatedCasesResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CriminalisticRelatedCasesResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticRelatedCasesResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFailure();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      failure_pb.FailureGrpc.serializeBinaryToWriter
    );
  }
  f = message.getRelatedcases();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ArrayOfRelatedCaseGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * optional FailureGrpc failure = 1;
 * @return {?proto.FailureGrpc}
 */
proto.CriminalisticRelatedCasesResponse.prototype.getFailure = function() {
  return /** @type{?proto.FailureGrpc} */ (
    jspb.Message.getWrapperField(this, failure_pb.FailureGrpc, 1));
};


/**
 * @param {?proto.FailureGrpc|undefined} value
 * @return {!proto.CriminalisticRelatedCasesResponse} returns this
*/
proto.CriminalisticRelatedCasesResponse.prototype.setFailure = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.CriminalisticRelatedCasesResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticRelatedCasesResponse} returns this
 */
proto.CriminalisticRelatedCasesResponse.prototype.clearFailure = function() {
  return this.setFailure(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticRelatedCasesResponse.prototype.hasFailure = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional ArrayOfRelatedCaseGrpcModel relatedCases = 2;
 * @return {?proto.ArrayOfRelatedCaseGrpcModel}
 */
proto.CriminalisticRelatedCasesResponse.prototype.getRelatedcases = function() {
  return /** @type{?proto.ArrayOfRelatedCaseGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.ArrayOfRelatedCaseGrpcModel, 2));
};


/**
 * @param {?proto.ArrayOfRelatedCaseGrpcModel|undefined} value
 * @return {!proto.CriminalisticRelatedCasesResponse} returns this
*/
proto.CriminalisticRelatedCasesResponse.prototype.setRelatedcases = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.CriminalisticRelatedCasesResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticRelatedCasesResponse} returns this
 */
proto.CriminalisticRelatedCasesResponse.prototype.clearRelatedcases = function() {
  return this.setRelatedcases(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticRelatedCasesResponse.prototype.hasRelatedcases = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.CriminalisticSubjectRelatedCasesResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.CriminalisticSubjectRelatedCasesResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  FAILURE: 1,
  CASERELATEDSUBJECTS: 2
};

/**
 * @return {proto.CriminalisticSubjectRelatedCasesResponse.ResponseCase}
 */
proto.CriminalisticSubjectRelatedCasesResponse.prototype.getResponseCase = function() {
  return /** @type {proto.CriminalisticSubjectRelatedCasesResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.CriminalisticSubjectRelatedCasesResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CriminalisticSubjectRelatedCasesResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.CriminalisticSubjectRelatedCasesResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CriminalisticSubjectRelatedCasesResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticSubjectRelatedCasesResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
failure: (f = msg.getFailure()) && failure_pb.FailureGrpc.toObject(includeInstance, f),
caserelatedsubjects: (f = msg.getCaserelatedsubjects()) && proto.ArrayOfSubjectRelatedCaseGrpcModel.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CriminalisticSubjectRelatedCasesResponse}
 */
proto.CriminalisticSubjectRelatedCasesResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CriminalisticSubjectRelatedCasesResponse;
  return proto.CriminalisticSubjectRelatedCasesResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CriminalisticSubjectRelatedCasesResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CriminalisticSubjectRelatedCasesResponse}
 */
proto.CriminalisticSubjectRelatedCasesResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new failure_pb.FailureGrpc;
      reader.readMessage(value,failure_pb.FailureGrpc.deserializeBinaryFromReader);
      msg.setFailure(value);
      break;
    case 2:
      var value = new proto.ArrayOfSubjectRelatedCaseGrpcModel;
      reader.readMessage(value,proto.ArrayOfSubjectRelatedCaseGrpcModel.deserializeBinaryFromReader);
      msg.setCaserelatedsubjects(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CriminalisticSubjectRelatedCasesResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CriminalisticSubjectRelatedCasesResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CriminalisticSubjectRelatedCasesResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticSubjectRelatedCasesResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFailure();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      failure_pb.FailureGrpc.serializeBinaryToWriter
    );
  }
  f = message.getCaserelatedsubjects();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ArrayOfSubjectRelatedCaseGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * optional FailureGrpc failure = 1;
 * @return {?proto.FailureGrpc}
 */
proto.CriminalisticSubjectRelatedCasesResponse.prototype.getFailure = function() {
  return /** @type{?proto.FailureGrpc} */ (
    jspb.Message.getWrapperField(this, failure_pb.FailureGrpc, 1));
};


/**
 * @param {?proto.FailureGrpc|undefined} value
 * @return {!proto.CriminalisticSubjectRelatedCasesResponse} returns this
*/
proto.CriminalisticSubjectRelatedCasesResponse.prototype.setFailure = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.CriminalisticSubjectRelatedCasesResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticSubjectRelatedCasesResponse} returns this
 */
proto.CriminalisticSubjectRelatedCasesResponse.prototype.clearFailure = function() {
  return this.setFailure(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticSubjectRelatedCasesResponse.prototype.hasFailure = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional ArrayOfSubjectRelatedCaseGrpcModel caseRelatedSubjects = 2;
 * @return {?proto.ArrayOfSubjectRelatedCaseGrpcModel}
 */
proto.CriminalisticSubjectRelatedCasesResponse.prototype.getCaserelatedsubjects = function() {
  return /** @type{?proto.ArrayOfSubjectRelatedCaseGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.ArrayOfSubjectRelatedCaseGrpcModel, 2));
};


/**
 * @param {?proto.ArrayOfSubjectRelatedCaseGrpcModel|undefined} value
 * @return {!proto.CriminalisticSubjectRelatedCasesResponse} returns this
*/
proto.CriminalisticSubjectRelatedCasesResponse.prototype.setCaserelatedsubjects = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.CriminalisticSubjectRelatedCasesResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticSubjectRelatedCasesResponse} returns this
 */
proto.CriminalisticSubjectRelatedCasesResponse.prototype.clearCaserelatedsubjects = function() {
  return this.setCaserelatedsubjects(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticSubjectRelatedCasesResponse.prototype.hasCaserelatedsubjects = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.CriminalisticLocationCasesResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.CriminalisticLocationCasesResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  FAILURE: 1,
  LOCATIONCASES: 2
};

/**
 * @return {proto.CriminalisticLocationCasesResponse.ResponseCase}
 */
proto.CriminalisticLocationCasesResponse.prototype.getResponseCase = function() {
  return /** @type {proto.CriminalisticLocationCasesResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.CriminalisticLocationCasesResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CriminalisticLocationCasesResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.CriminalisticLocationCasesResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CriminalisticLocationCasesResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticLocationCasesResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
failure: (f = msg.getFailure()) && failure_pb.FailureGrpc.toObject(includeInstance, f),
locationcases: (f = msg.getLocationcases()) && proto.ArrayOfCaseLocationGrpcModel.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CriminalisticLocationCasesResponse}
 */
proto.CriminalisticLocationCasesResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CriminalisticLocationCasesResponse;
  return proto.CriminalisticLocationCasesResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CriminalisticLocationCasesResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CriminalisticLocationCasesResponse}
 */
proto.CriminalisticLocationCasesResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new failure_pb.FailureGrpc;
      reader.readMessage(value,failure_pb.FailureGrpc.deserializeBinaryFromReader);
      msg.setFailure(value);
      break;
    case 2:
      var value = new proto.ArrayOfCaseLocationGrpcModel;
      reader.readMessage(value,proto.ArrayOfCaseLocationGrpcModel.deserializeBinaryFromReader);
      msg.setLocationcases(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CriminalisticLocationCasesResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CriminalisticLocationCasesResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CriminalisticLocationCasesResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticLocationCasesResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFailure();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      failure_pb.FailureGrpc.serializeBinaryToWriter
    );
  }
  f = message.getLocationcases();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.ArrayOfCaseLocationGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * optional FailureGrpc failure = 1;
 * @return {?proto.FailureGrpc}
 */
proto.CriminalisticLocationCasesResponse.prototype.getFailure = function() {
  return /** @type{?proto.FailureGrpc} */ (
    jspb.Message.getWrapperField(this, failure_pb.FailureGrpc, 1));
};


/**
 * @param {?proto.FailureGrpc|undefined} value
 * @return {!proto.CriminalisticLocationCasesResponse} returns this
*/
proto.CriminalisticLocationCasesResponse.prototype.setFailure = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.CriminalisticLocationCasesResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticLocationCasesResponse} returns this
 */
proto.CriminalisticLocationCasesResponse.prototype.clearFailure = function() {
  return this.setFailure(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticLocationCasesResponse.prototype.hasFailure = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional ArrayOfCaseLocationGrpcModel locationCases = 2;
 * @return {?proto.ArrayOfCaseLocationGrpcModel}
 */
proto.CriminalisticLocationCasesResponse.prototype.getLocationcases = function() {
  return /** @type{?proto.ArrayOfCaseLocationGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.ArrayOfCaseLocationGrpcModel, 2));
};


/**
 * @param {?proto.ArrayOfCaseLocationGrpcModel|undefined} value
 * @return {!proto.CriminalisticLocationCasesResponse} returns this
*/
proto.CriminalisticLocationCasesResponse.prototype.setLocationcases = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.CriminalisticLocationCasesResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticLocationCasesResponse} returns this
 */
proto.CriminalisticLocationCasesResponse.prototype.clearLocationcases = function() {
  return this.setLocationcases(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticLocationCasesResponse.prototype.hasLocationcases = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.NumberResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.NumberResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  FAILURE: 1,
  VALUE: 2
};

/**
 * @return {proto.NumberResponse.ResponseCase}
 */
proto.NumberResponse.prototype.getResponseCase = function() {
  return /** @type {proto.NumberResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.NumberResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.NumberResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.NumberResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.NumberResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NumberResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
failure: (f = msg.getFailure()) && failure_pb.FailureGrpc.toObject(includeInstance, f),
value: (f = jspb.Message.getField(msg, 2)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.NumberResponse}
 */
proto.NumberResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.NumberResponse;
  return proto.NumberResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.NumberResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.NumberResponse}
 */
proto.NumberResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new failure_pb.FailureGrpc;
      reader.readMessage(value,failure_pb.FailureGrpc.deserializeBinaryFromReader);
      msg.setFailure(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setValue(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.NumberResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.NumberResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.NumberResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.NumberResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFailure();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      failure_pb.FailureGrpc.serializeBinaryToWriter
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeInt32(
      2,
      f
    );
  }
};


/**
 * optional FailureGrpc failure = 1;
 * @return {?proto.FailureGrpc}
 */
proto.NumberResponse.prototype.getFailure = function() {
  return /** @type{?proto.FailureGrpc} */ (
    jspb.Message.getWrapperField(this, failure_pb.FailureGrpc, 1));
};


/**
 * @param {?proto.FailureGrpc|undefined} value
 * @return {!proto.NumberResponse} returns this
*/
proto.NumberResponse.prototype.setFailure = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.NumberResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.NumberResponse} returns this
 */
proto.NumberResponse.prototype.clearFailure = function() {
  return this.setFailure(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.NumberResponse.prototype.hasFailure = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional int32 value = 2;
 * @return {number}
 */
proto.NumberResponse.prototype.getValue = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.NumberResponse} returns this
 */
proto.NumberResponse.prototype.setValue = function(value) {
  return jspb.Message.setOneofField(this, 2, proto.NumberResponse.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.NumberResponse} returns this
 */
proto.NumberResponse.prototype.clearValue = function() {
  return jspb.Message.setOneofField(this, 2, proto.NumberResponse.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.NumberResponse.prototype.hasValue = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.CriminalisticCaseCoincidenceResponse.oneofGroups_ = [[1,2]];

/**
 * @enum {number}
 */
proto.CriminalisticCaseCoincidenceResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  FAILURE: 1,
  CASECOINCIDENCE: 2
};

/**
 * @return {proto.CriminalisticCaseCoincidenceResponse.ResponseCase}
 */
proto.CriminalisticCaseCoincidenceResponse.prototype.getResponseCase = function() {
  return /** @type {proto.CriminalisticCaseCoincidenceResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.CriminalisticCaseCoincidenceResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.CriminalisticCaseCoincidenceResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.CriminalisticCaseCoincidenceResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.CriminalisticCaseCoincidenceResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticCaseCoincidenceResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
failure: (f = msg.getFailure()) && failure_pb.FailureGrpc.toObject(includeInstance, f),
casecoincidence: (f = msg.getCasecoincidence()) && proto.CaseCoincidenceGrpcModel.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.CriminalisticCaseCoincidenceResponse}
 */
proto.CriminalisticCaseCoincidenceResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.CriminalisticCaseCoincidenceResponse;
  return proto.CriminalisticCaseCoincidenceResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.CriminalisticCaseCoincidenceResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.CriminalisticCaseCoincidenceResponse}
 */
proto.CriminalisticCaseCoincidenceResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new failure_pb.FailureGrpc;
      reader.readMessage(value,failure_pb.FailureGrpc.deserializeBinaryFromReader);
      msg.setFailure(value);
      break;
    case 2:
      var value = new proto.CaseCoincidenceGrpcModel;
      reader.readMessage(value,proto.CaseCoincidenceGrpcModel.deserializeBinaryFromReader);
      msg.setCasecoincidence(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.CriminalisticCaseCoincidenceResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.CriminalisticCaseCoincidenceResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.CriminalisticCaseCoincidenceResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.CriminalisticCaseCoincidenceResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFailure();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      failure_pb.FailureGrpc.serializeBinaryToWriter
    );
  }
  f = message.getCasecoincidence();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.CaseCoincidenceGrpcModel.serializeBinaryToWriter
    );
  }
};


/**
 * optional FailureGrpc failure = 1;
 * @return {?proto.FailureGrpc}
 */
proto.CriminalisticCaseCoincidenceResponse.prototype.getFailure = function() {
  return /** @type{?proto.FailureGrpc} */ (
    jspb.Message.getWrapperField(this, failure_pb.FailureGrpc, 1));
};


/**
 * @param {?proto.FailureGrpc|undefined} value
 * @return {!proto.CriminalisticCaseCoincidenceResponse} returns this
*/
proto.CriminalisticCaseCoincidenceResponse.prototype.setFailure = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.CriminalisticCaseCoincidenceResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticCaseCoincidenceResponse} returns this
 */
proto.CriminalisticCaseCoincidenceResponse.prototype.clearFailure = function() {
  return this.setFailure(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticCaseCoincidenceResponse.prototype.hasFailure = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional CaseCoincidenceGrpcModel caseCoincidence = 2;
 * @return {?proto.CaseCoincidenceGrpcModel}
 */
proto.CriminalisticCaseCoincidenceResponse.prototype.getCasecoincidence = function() {
  return /** @type{?proto.CaseCoincidenceGrpcModel} */ (
    jspb.Message.getWrapperField(this, proto.CaseCoincidenceGrpcModel, 2));
};


/**
 * @param {?proto.CaseCoincidenceGrpcModel|undefined} value
 * @return {!proto.CriminalisticCaseCoincidenceResponse} returns this
*/
proto.CriminalisticCaseCoincidenceResponse.prototype.setCasecoincidence = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.CriminalisticCaseCoincidenceResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.CriminalisticCaseCoincidenceResponse} returns this
 */
proto.CriminalisticCaseCoincidenceResponse.prototype.clearCasecoincidence = function() {
  return this.setCasecoincidence(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.CriminalisticCaseCoincidenceResponse.prototype.hasCasecoincidence = function() {
  return jspb.Message.getField(this, 2) != null;
};


goog.object.extend(exports, proto);
