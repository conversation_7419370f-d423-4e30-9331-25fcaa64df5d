import {
    SegmentedSearchFilter
} from "src/verazial-common-frontend/core/general/manager/common/models/segmented-search-filter.model";


export class CaseCoincidenceEntity {
    id?: string;
    caseId?: string;
    evidenceId?: string;
    coincidenceSubjectId?: string;
    type?: string;
    status?: string;
    score?: number;
    rawId?: string;
    searchFilters?: SegmentedSearchFilter[];
    authUserId?: string;
    authUserNumId?: string;
    authUserSignatureTech?: string;
    authUserSignatureDate?: Date;
    content?: string;
    mimeType?: string;
    createdBy?: string;
    createdAt?: Date;
    updatedBy?: string;
    updatedAt?: Date;
}