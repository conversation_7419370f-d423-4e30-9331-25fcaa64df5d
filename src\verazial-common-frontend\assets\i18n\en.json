{"locale": "en-US", "add_application": "Add Application", "add_window": "Add Window", "dateFormat": "mm/dd/yy", "today": "Today", "dateFormatLong": "MM/dd/yyyy", "dateTimeFormat": "MM/dd/yyyy hh:mm:ss aa", "timeOnlyFormat": "hh:mm:ss aa", "hourFormat": "12", "username": "Username", "password": "Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "setupNewAdminUser": "New Administrator", "updatePassword": "Update Password", "resetPassword": "Reset Password", "versions": "Versions", "save": "Save", "add": "Add", "confirm": "Confirm", "clear": "Clear", "continue": "Continue", "accept": "Accept", "cancel": "Cancel", "close": "Close", "search_coincidences": "Search Coincidences", "view_full_profile": "View full profile", "update": "Update", "delete": "Delete", "delete_entity": "Delete", "edit_entity": "Edit En<PERSON>", "edit": "Edit", "view_data": "View Data", "create_new_user": "New User", "create_new_subject": "New Subject", "create_new_case": "New Case", "createRecord": "Create Subject", "auditTrail": "Audit Trail", "next": "Next", "after_searching_by_id": "After Searching by ID, <PERSON>ow", "disconnect": "Disconnect", "manageProfile": " Manage Profile", "logout": " Log out", "spanish": "Spanish", "english": "English", "portuguese": "Portuguese", "yes": "Yes", "no": "No", "publish": "Publish", "back": "Back", "created_at": "Created at", "updated_at": "Updated at", "profilePicture": "Profile picture", "searchInRecords": "Search in records", "searchInUsers": "Search in users", "biographic": "Biographic", "biometric": "Biometric", "prisons": "Prisons", "history": "History", "actions": "Actions", "flowAssignment": "Flow Assignment", "flows": "Flows", "profiles": "Profiles", "change_view": "Change View", "verify": "Verify", "identify": "Identify", "verify_identity": "Verify identity", "verify_user": "Verify user", "datePlaceholder": "dd/mm/yyyy", "ADMIN": "Administrator", "ENROLLER": "Enroller", "clearFilters": "Clear filters", "update_password": "Update password", "remove": "Remove", "confirmation": "Confirmation", "subject": {"new_subject": "New Subject", "no_subjects_available": "No subjects available"}, "buttons": {"save": "Save", "show": "Show", "logout": "Logout", "add_option": "Add option", "add_relation": "Add relation", "add_segment": "Add segment", "add_location": "Add location", "add_device": "Add device", "new": "New", "cancel": "Cancel", "yes": "Yes", "no": "No", "sign_in": "Sign in", "verify": "Verify", "apply": "Apply", "update_password": "Update Password", "continue": "Continue", "add": "Add", "new_license": "New license", "new_api_connection": "New API connection", "add_method": "Add method", "next": "Next", "back": "Back", "update": "Update", "new_ldap_connection": "New LDAP connection", "return": "Return", "add_data_source": "Add Datasource", "add_parameters": "Add Parameters", "add_window": "Add Window", "show_data_sources": "Show Datasources", "show_parameters": "Show Parameters", "show_application": "Show Applications", "show_windows": "Show Windows", "change_password": "Change password", "reload": "Reload", "update_credentials": "Update credentials", "new_application": "New application", "publish": "Publish", "unpublish": "Unpublish", "new_window": "New window", "new_component": "New component", "send": "Send", "new_datasource": "New Datasource", "finish": "Finish", "add_parameter": "Add Parameter", "returnHome": "Return to home page", "sendPasswordRecovery": "Send password recovery", "add_location_table": "Add location", "close": "Close"}, "menu": {"locations": "Locations", "subject_locations": "Subject Locations", "main": "Main", "home": "Home", "search": "Search", "subject": "Subject", "subjects": "Subjects", "enroll": "Enroll", "new_subject": "New Subject", "biographic_data": "Biographic Data", "facial": "Facial", "iris": "Iris", "all": "All", "all_m": "All", "all_f": "All", "fingerprint": "Fingerprint", "rolledFingerPrint": "Rolled Fingerprint", "palm": "Palm", "additional_info": "Additional Info", "summary": "Summary", "administration": "Administration", "tenants": "Tenants", "roles": "Roles", "profile_role": "Profiles/Roles", "users": "Users", "process": "Process", "clock": "Clock", "flows": "Flows", "categories": "Categories", "assignments": "Assignments", "time_records": "Time Records", "audit_trail": "Audit Trail", "pass": "Pass", "application_datasource": "Datasource", "application_flow": "Application Processes", "application_assign": "Application Assignment", "application_settings": "Application Settings", "versions": "Versions", "admin_users": "User", "config_role": "Config. Role", "config_admin": "ID Admin", "config_enroll": "ID Enroll", "config_prisons": "ID Prisons", "config_widget": "ID Widget", "config_clock": "ID Clock", "config_pass": "ID Pass", "config_manager": "ID Manager", "config_home": "<PERSON><PERSON>o", "config_reports": "ID Reports", "config_general": "General", "configuration": "Configuration", "datasources": "Datasources", "applications": "Applications", "my_applications": "My Applications", "reports": "Analitics", "reports_general": "General", "reports_users": "Users", "reports_subjects": "Subjects", "reports_verification": "Verification", "reports_identification": "Identification", "reports_performance": "Performance", "reports_actions": "Actions", "reports_audit_trail": "Audit trail", "prisons": "Prisons", "prisons_visits": "Visits", "prisons_schedules": "Schedules", "prisons_transfers": "Transfers", "criminalistics": "Criminalistics", "criminalistics_reports": "Reports", "criminalistics_cases": "Current Cases", "fingerprint_form": "Fingerprint Form"}, "divider": {"new_action": "New Action", "or_continue_with": "or continue with", "new_reason": "New reason", "new_profile": "New profile", "new_segment": "New segment", "matcher": "Matcher", "edit_licenses": "Edit licenses", "new_location": "New location", "new_device": "New device", "credentials": "Credentials", "match": "Match", "enroll": "Enroll", "new_field": "New field", "add_fields": "Add fields", "new_theme": "New theme", "connection": "Connection", "default_datasource": "Default datasource", "new_method": "New method", "new_license": "New license", "add_catalog": "Add catalog", "new_app_flow_type": "New flow type"}, "loginForm": {"enterEmail": "Enter your email", "enterUsername": "Enter your username", "enterPassword": "Enter the password", "enterNumID": "Enter the ID number", "loginOptions": "Login Options", "userNotFound": "User not found", "wrongUsername": "Wrong username", "wrongPassword": "Wrong password", "wrongCredentials": "Wrong credentials", "adminBioLabel": "or continue with", "subjectBioLabel": "Verify your identity", "signIn": "Sign in", "back_to_login": "Back to login"}, "table": {"num_id": "ID number", "numId": "ID number", "profile": "Profile", "main_profile": "Main Profile", "profiles": "Profiles", "role": "Role", "roles": "Roles", "main_role": "Main Role", "verification": "1:1 Verification", "segmented_search": "1:n Segmented search", "username": "Username", "first_name": "First Name", "second_name": "Second Name", "last_name": "Last Name", "second_last_name": "Second Last Name", "names": "Names", "lastNames": "Last Names", "birthdate": "Birthdate", "gender": "Gender", "language": "Language", "datasource": "Datasource", "actions": "Actions", "flowAssignment": "Flow Assignment", "last_action": "Last Action", "reasonUpdate": "Select the reason to update", "reasonDelete": "Select the reason to delete", "reasonCreate": "Select the reason to create", "reasonConfig": "Select the reason to modify the configuration", "center": "Center", "especific_location": "Specific Location", "present/absent": "Present/Absent (hh:mm:ss)", "present": "Present", "absent": "Absent", "transfer": "Transfer", "lastActions": "Last Actions", "assignedTo": "Assigned to", "caseNumber": "Case number", "name": "Name", "createdBy": "Created by", "updatedBy": "Updated by", "createdAt": "Created at", "updatedAt": "Updated at", "openDate": "Opened at", "subject_profiles": "Subject profiles", "user_roles": "User roles", "assignedType": "Assigned"}, "action_codes": {"no_device": "NO_DEV", "modify_user": "MOD_BIO", "modify_config": "MOD_CONF", "remove_subject_sample": "REM_SUB_SAM", "remove_subject_button": "REM_SUB_BT", "remove_picture": "REM_PIC", "remove_sample": "REM_SAM", "add_subject": "NEW_SUB", "add_sample": "NEW_SAM", "add_picture": "ADD_PIC", "verification_initial": "VER_INI", "verification_complementary": "VER_COM", "identification_biometrics": "ID_SAM", "identification_user_pass": "ID_PASS", "identification_otp": "ID_KEY", "identification_pin": "ID_PIN", "debug_local": "DEB_LOC", "modify_prisons": "MOD_PR"}, "prisons_tab": {"location": "Location", "occupiedBy": "Occupied by", "relationships": "Relationships", "entry_date": "Entry Date", "init_date": "Init Date", "end_date": "End Date", "exit_date": "Exit Date", "reason": "Reason for Entry/Exit", "visited_person": "Visited Person", "visit_time": "Visit Time", "is_inside": "Is Inside the Facility?", "cpl": "CPL", "regime": "Regime", "clasification": "Clasification", "pending": "Pending", "actual_location": "Actual Location", "new_location": "Assign new location", "new_entry_exit": "New Entry/Exit", "definitive_entry": "Definitive Entry", "definitive_exit": "Definitive Exit", "entry": "Entry", "exit": "Exit", "transfer_entry": "Transfer Entry", "transfer_exit": "Transfer Exit", "select_location_entry": "Select location for entry", "select_related_subject": "Select related subject for entry", "select_transfer_auth": "Select transfer authorization for exit", "select_entry_and_exit_auth": "Select entry and exit authorization"}, "actions_tab": {"action_code": "Action Code", "action_name": "Action Name"}, "config": {"auto_delete": "Automatic Biometric Data Deletion", "delete": "Delete", "widget_options": "Widget Options", "url_widget": "URL WIDGET", "widget_match_key": "WIDGET MATCH KEY", "widget_enroll_key": "WIDGET ENROLL KEY", "biometricServerComponent_options": "Biometric Server Options", "biometricServerUrl": "URL BIOMETRIC SERVER", "biometricServerUser": "Username", "biometricServerPassword": "Password", "biographicServerComponent_options": "Biographic Server Options", "biographicServerUrl": "URL BIOGRAPHIC SERVER", "biographicServerUser": "Username", "biographicServerPassword": "Password", "externalBiographicServerComponent_options": "External Biographic Server Options", "externalBiographicServer": "External Biographic Server", "externalBiographicServerUrl": "URL EXTERNAL BIOGRAPHIC SERVER", "externalBiographicServerUser": "Username", "externalBiographicServerPassword": "Password", "clockServerComponent_options": "Clock Server Options", "clockServerUrl": "URL CLOCK SERVER", "clockServerUser": "Username", "clockServerPassword": "Password", "storageServerComponent_options": "Storage Server Options", "storageServerUrl": "URL STORAGE SERVER", "storageServerUser": "Username", "storageServerPassword": "Password", "actions available": "Available Actions", "show_table": "Show Table", "code": "Code", "name": "Name", "filter_action": "Filter Action Name", "filter_profile": "Filter Profile Name", "close": "Close", "subjectTabs": "Subject Page Tab Configuration", "showAdditionalTabs": "Show Aditional Tabs", "showPrisonTab": "Show Prison Tab", "showProfilePictureTab": "Show Profile Picture History Tab", "allowAfternIdSearch": "After searching by ID, allow", "allowEditAfternIdSearch": "Edit Subject", "allowDeleteAfterIdSearch": "Delete Subject", "actionsServerComponent_options": "Actions Server Options", "actionsServerUrl": "URL ACTIONS SERVER", "actionsServerUser": "Username", "actionsServerPassword": "Password"}, "groups": {"new_group": "New Group", "no_groups_available": "No groups available", "group_type": "Group Type", "configuration": "Configuration", "group": "group", "delete_group": "Delete group", "update_group": "Update group"}, "category": {"new_category": "New Category", "no_categories_available": "No categories available", "category_type": "Category", "configuration": "Configuration", "category": "Category", "delete_category": "Delete category", "update_category": "Update category", "list_categories": "List of categories", "selected_categories": "Categories selected", "enable_date_range": "Date range"}, "flow": {"code": "Code", "name": "Name", "description": "Description", "profiles": "Profiles", "showFlowActions": "Show Flow Actions", "predecessors": "Predecessors", "action": "Action", "new_flow": "New Flow", "no_flows_available": "No flows available", "remove_flow": "Remove Flow", "add_action": "Add Action", "actions_availables": "<PERSON>'s actions", "drag_drop": "Drag and drop ", "the_action": "the action", "new_action": "New action", "edit_action": "Edit Action", "attributes": "Attributes", "select_element": "Select an element", "input": "Input", "dropdown": "Dropdown", "toggle": "Toggle", "required": "Required", "user": "User", "option": "Option", "options": "Options", "remove_attribute": "Remove Attribute", "remove_action": "Remove action", "save_flow": "Save Flow", "status": "Status", "updated_at": "Updated at", "published": "Published", "unpublished": "Remove publication", "no_published": "No published", "update_flow": "Update Flow", "last_update": "Last update", "publish": "Publish", "flow": "Flow", "button": "<PERSON><PERSON>", "is_relay": "Is relay", "list_flows": "List of flows", "selected_flows": "Flows selected", "min_characters": "Min. number of characters", "max_characters": "Max. number of characters", "message": "Message", "no_actions": "No actions", "severity": "Severity"}, "assignment": {"name": "Name", "num_flows": "Num. flows", "num_categories": "Num. categories", "updated_at": "Updated at", "new_assignment": "New assignment", "update_assignment": "Update assignment", "general": "General", "add_flow": "Add flow", "add_category": "Add category", "assignment": "assignment", "delete_assignment": "Delete assignment", "no_assignment_available": "No assignments available", "flows": "Flows", "categories": "Categories", "configuration": "Configuration", "locations": "Locations", "subjects": "Subjects", "schedules": "Schedules"}, "user": {"num_id": "ID NUMBER", "profile": "PROFILE", "username": "USERNAME", "names": "NAMES", "lastNames": "LAST NAMES", "first_name": "FIRST NAME", "second_name": "SECOND NAME", "last_name": "LAST NAME", "second_last_name": "SECOND LAST NAME", "birthdate": "BIRTHDATE", "gender": "GENDER", "language": "LANGUAGE", "location": "LOCATION", "segment": "SEGMENT", "device": "DEVICE", "no_roles_assigned": "No roles assigned", "new_user": "New user", "no_users_available": "No users available."}, "signaturesTable": {"identityId": "Identity", "eventCode": "Action", "eventTimestamp": "Timestamp", "locationId": "Location", "segmentId": "Segment", "deviceId": "<PERSON><PERSON>", "eventLatitude": "Lat.", "eventLongitude": "<PERSON>.", "applicationId": "Application ID", "id": "ID", "eventId": "Event ID", "startDate": "Start Date", "endDate": "End Date", "eventResource": "Event Resource", "eventData": "Event Data", "executorId": "User", "receiverId": "Subject", "receiverProfile": "Subject Profile", "actionId": "Action ID", "actionDuration": "Duration", "actionResult": "Result", "technologyId": "Technology", "samplesNumber": "Number of samples", "samplesSequentially": "samplesSequentially", "sensorBrand": "Sensor Brand", "sensorModel": "Sensor Model", "attributes": "Attributes", "numericAttributes": "Numeric Attributes", "samples": "<PERSON><PERSON>", "coordinates": "Coordinates", "action": "Action", "result": "Result", "executor": "Executor", "receptor": "Receiver", "date": "Date", "totalTime": "Total time", "details": "Details", "userTime": "User time", "serverTime": "Server time", "connectionTime": "Network time", "extractionTime": "Extraction time", "matchingTime": "Matching time"}, "tenant": {"name": "Name", "nif": "CID", "email": "email", "country": "Country", "city": "City", "status": "Status", "is_active": "Active", "created_at": "Created at", "updated_at": "Updated at", "no_tenants_available": "No tenants avaiable", "new_tenant": "New tenant", "tenant": "Tenant", "wait_tenant_creation": "Please wait until the tenant creation process is fully completed.", "engine": "Engine", "host": "Host", "port": "Port", "database": "Database", "schema": "<PERSON><PERSON><PERSON>", "username": "Username", "password": "Password", "database_config": "Database Configuration", "change_tenant": "Change Tenant", "no_admin_token": "No admin token"}, "titles": {"subjects": "Subjects", "subjectsShort": "Subjects", "records": "Subjects", "recordsShort": "Subjects", "fingerPrint": "Fingerprint", "facial": "Facial", "iris": "Iris", "rolledFingerPrint": "Rolled Fingerprint", "palm": "Palm", "flatFingerPrint": "Flat Fingerprint", "administration": "Administration", "administrationShort": "Admin", "settings": "Settings", "signatures": "Time Records", "auditTrail": "Audit Trail", "error_general": "An error has occurred", "error_requiredField": "Required field", "success_general": "Completed successfully", "error_permisionDenied": "Permission denied", "error_subjectNotFound": "Error: Subject not found", "error_dataInconsistency": "Data inconsistency", "personalData": "Personal Data", "editSubject": "Edit subject", "edit_subject": "Edit subject", "edit_user": "Edit user", "exitingWithoutSaving": "Unsaved changes", "lastActionDetails": "Last action details", "success_operation": "Success", "info_operation": "Information registered", "error_operation": "Operation error", "success_enroll_user": "Subject enrolled successfully", "success_add_user": "Subject created successfully", "success_modify_user": "Subject modified successfully", "success_delete_user": "Subject deleted successfully", "success_delete_picture": "User picture deleted successfully", "success_delete_sample": "Biometric sample deleted successfully", "success_add_picture": "Profile image added successfully", "error_enroll_user": "Subject not enrolled", "error_add_user": "Subject not created", "error_modify_user": "Subject not modified", "error_delete_user": "Subject not deleted", "error_delete_picture": "Subject picture not deleted", "error_delete_sample": "Biometric sample not deleted", "error_add_picture": "Profile image not registered", "success_identify_user": "Subject identified successfully", "error_identify_user": "Subject not found", "success_auth_user": "Subject identified successfully", "error_auth_user": "Subject authenticated successfully", "error_data_inconsistency": "Data inconsistency detected", "access_granted": "Access granted", "access_denied": "Access denied", "flows": "Flows", "users": "Users", "groups": "Groups", "categories": "Categories", "process": "Process", "assignment": "Assignments", "login_error": "Login error", "role": "Roles", "profile_role": "Profiles/Roles", "page_not_found": "Page not Found", "unauthorized": "Forbidden", "biometric_data": "Biometric Data", "biographic_data": "Biographic Data", "extended_biographic_data": "Extended Biographic Data", "physical_data": "Physical Data", "profile_pic_history": "Profile Picture History", "related_subjects": "Related Subjects", "entries_and_exits": "Entries and Exits", "entry_exit_authorizations": "Entry/Exit Authorizations", "tenants": "Tenants", "important": "Important", "konektor": "Konektor", "konektor_properties": "Konektor Properties", "auth": "Authentication", "systemSettings": "System Settings", "error_role_access": "Role access error", "error_login": "Login error", "widget": "Widget", "unable_to_delete_subject": "Unable to delete subject", "unable_to_perform_action": "Unable to perform action", "CameraError": "Error accessing the camera", "error": "Error", "samples": "<PERSON><PERSON>", "samples_subjects": "Samples by Subject", "profile_pics": "Profile pictures", "unknown": "Unknown", "requests": "Requests", "verified": "Verified", "notVerified": "Not verified", "found": "Found", "notFound": "Not found", "qFailed": "Poor quality", "tError": "Server timeout", "sError": "Server error", "other": "Other", "actionV": "Verification 1:1", "actionI": "Identification 1:N", "actionA": "Add sample", "actionD": "Delete sample", "actionP": "Add picture", "actionS": "Add subject", "actionR": "Delete subject", "userTime": "User time", "connTime": "Network time", "serverTime": "Server time", "picYes": "With profile picture", "picNo": "Without profile picture", "male": "Male", "female": "Female", "otherGender": "Other", "error_change_tenant": "Error changing tenant", "belongings": "Belongings", "no_belongings_records_available": "No belongings records available", "user_creation": "User creation", "subject_creation": "Subject creation", "add_profile_pic_history": "Add Profile Picture History", "belongings_photos": "Belongings Photos", "belongings_hand_over_signatures": "Belongings Hand Over Signatures", "belongings_return_signatures": "Belongings Return Signatures", "judicial_files": "Judicial Files", "prisons_visits": "Prisons Visits", "authorization_schedules": "Authorization Schedules", "authorization_schedules_to_enter_centers": "Authorization Schedules to Enter Centers", "transfer_authorization": "Transfer Authorizations", "no_judicial_files_records_available": "No judicial files records available", "no_transfer_auths_available": "No transfer authorizations available", "no_entry_exit_auths_available": "No entry/exit authorizations available", "no_auth_schedules_available": "No authorization schedules available", "subject_files": "Files", "cases": "Cases", "evidence": "Evidence", "coincidences": "Coincidences", "case_info": "Case Information", "warning": "Warning"}, "title": {"settings": "Settings", "users": "Users", "applications": "Assigning applications", "credential_management": "Credential Management", "change_password": "Change Password"}, "application": {"ROLE_APP_CLOCK": "ID Clock", "ROLE_APP_PASS": "ID Pass", "ROLE_APP_MANAGER": "ID Manager", "ROLE_APP_ADMIN": "ID Admin", "ROLE_APP_REPORTS": "ID Reports", "ROLE_APP_WIDGET_MATCH": "ID Widget Match", "ROLE_APP_WIDGET_ENROLL": "ID Widget Enroll", "ROLE_APP_WIDGET": "ID Widget"}, "cases": {"case": "Case", "new": "New case", "edit": "Edit case", "view": "View case", "new_evidence": "Add Evidence", "general_info": "General Information", "additional_info": "Additional Information", "locations": "Locations", "location": "Location", "search": "Search the area", "assignments_relations": "Assignments and Relationships", "name": "Name", "number": "Case Number", "priority": "Priority Level", "crime_type": "Type of Crime", "category": "Legal Category", "department": "Department", "status": "Case Status", "crime_date": "Incident Date", "open_date": "Opening Date", "close_date": "Closing Date", "description": "Description", "location_title": "Location Title", "location_type": "Location Type", "comments": "Comments", "relations": "Relations", "assignments": "Assignments", "principal_agent": "Principal Agent", "assigned_agents": "Assigned Agents", "related_cases": "Related Cases", "related_subjects": "Related Subjects", "under_investigation": "Under Investigation", "open": "Open", "resolved": "Resolved", "inactive": "Inactive", "waiting": "Waiting for Review", "closed": "Closed", "primary": "Primary", "secondary": "Secondary", "additional": "Aditional", "pending": "Pending", "analyzed": "Analyzed", "confirmed": "Confirmed", "edited": "Edited", "new_comment": "New comment", "comment": "Comment", "mark_coincidence": "<PERSON>", "mark_coincidence_message": "Are you sure you want to mark this subject's sample as a ", "case_coincidence": "Case match", "search_configuration": "Search Configuration", "filters": "Filters", "max_results": "Maximum Results", "min_coincidence": "Minimum Match Percentage", "coincidence_name": "Coincidence Name", "evidence_name": "Evidence Name", "coincidence_status": "Status", "coincidence_score": "Match Level", "last_modification": "Last Modification", "modified_by": "Modified By", "coincidence_type": "Type of Coincidence", "more_info": "More Information", "coincidence_details": "Match details", "score_tittle": "Likelihood Percentage", "search_filters_tittle": "Applied Search Filters", "facial_subject_sample": "Facial Subject Sample"}, "messages": {"contactYourSystemAdministrator": "Please contact your system administrator", "pass_automation": "Pass Automation", "confirm_delete_profile": "Confirm you want to delete the record", "confirm_delete_multiple_profiles": "Confirm you want to delete the records", "will_be_deleted_profile": "The record with the following ID will be deleted:", "will_be_deleted_multiple_profile": "The records selected will be deleted.", "error_general": "An unexpected error has ocurred", "error_getting_actions": "Error getting actions", "FAILURE": "An unexpected error has ocurred", "error_permisionDenied": "This action cannot be performed", "error_isRequiredField": "Is a required field", "error_username_must_be_valid_numId": "The username must be a valid ID number", "error_username_must_be_valid_email": "The username must be a valid email", "error_must_be_valid_email": "Must be a valid email", "success_general": "The action has been completed successfully.", "error_subjectNotFound": "The subject does not exist in the database", "error_subjectNotFoundCreate": "The subject does not exist in the database, please create the subject in the biographical system or delete its biometric data", "error_dataInconsistency": "The identity exists on Verázial ID Server, but not on your biographic server. Please contact your system administrator.", "confirm_delete_flow": "Confirm you want to delete the flow", "will_be_deleted_flow": "The flow with the following Name will be deleted:", "confirm_delete_config_profile": "Confirm you want to delete the profile", "will_be_deleted_config_profile": "The profile with the following Name will be deleted:", "error_codeAlreadyExists": "The code provided is already created", "error_nameAlreadyExists": "The name provided is already created", "error_parameterAlreadyExists": "The parameter provided is already created", "error_profileAlreadySelected": "The profiles selected are already assigned:", "error_actionCodeAlreadyExists": "The action provided is already created", "error_dataAlreadyExists": "The data provided is already created", "success_flowActionUpdated": "The action provided updated successfully", "ERROR_PERMISSION_DENIED": "Permission denied, this action cannot be performed", "ERROR_WRONGCREDENTIALS": "Wrong credentials", "ERROR_BIOGRAFIC_REMOVEIDENTITY_NOT_COMPLETED": "Error on the automatic biometric data deletion: biographic/removeIdentity not completed", "ERROR_BIOMETRIC_UNENROLLSUBJECT_NOT_COMPLETED": "Error on the automatic biometric data deletion: biometric/unenrollSubject not completed", "ERROR_EMPTY_PARAMETER": "Empty parameter on call", "ERROR_USERS_TENANT_CONFIG_NOT_FOUND": "ERROR_USERS_TENANT_CONFIG_NOT_FOUND", "ERROR_CONFIG_NOT_FOUND": "Configuration not found", "ERROR_CONFIG_DATA_NOT_FOUND": "Configuration data not found", "confirmPasswordFailed": "The new password and the confirmation password do not match", "exitingWithoutSaving": "You have not saved the changes. Are you sure you want to exit?", "continueWithoutSaving": "You have not saved the changes. Are you sure you want to continue?", "selectTechnology": "Select a biometric technology to perform the verification for the subject:", "selectTechnologyUserVerification": "Select a biometric technology to perform the user verification", "error_dateRangeError": "Start date cannot be greater than end date", "ERROR_USER_IS_BLOCKED": "User is blocked", "ERROR_BIOMETRIC_VERIFICATION_FAILED": "Biometric verification failed", "ERROR_INVALID_NUMID": "Invalid ID number", "confirm_delete_profile_picture": "Are you sure you want to delete the profile picture?", "will_be_deleted_profile_picture": "The profile picture for the following subject will be deleted:", "user_without_samples_tec_selected": "The user does not have biometric samples in the technology selected", "user_without_samples": "The user does not have biometric samples", "quality_error": "The image quality is insufficient for identification", "invalidPassword": "Password must be at least 12 characters, and have at least one digit, one uppercase letter, one lowercase letter, and one special character", "invalidPasswordLength": "Minimum password length is ", "invalidPasswordComplexity": "The password doesn't meet the complexity requirements", "invalidAccessKey": "Invalid access key", "accessKeyDisabled": "Access key disabled, please contact your administrator", "disableOnNextAttempt": "Access key will be disabled on next attempt", "needLocationToEntry": "The subject needs a pending location to enter", "noLocationToEnterHere": "The subject does not have any location to enter here", "noLocationToEnterTransfer": "The subject does not have any transfer's valid location", "subjectMustReturnToSameLocation": "The subject must return to the same location", "noAuthScheduleForRole": "The subject's role does not have an authorization schedule", "noAuthScheduleForLocationAndRole": "The subject's role does not have an authorization schedule for this location", "noValidAuthScheduleForLocationAndRole": "The subject's role does not have a valid authorization schedule for this location", "noRelatedSubjectsOfEntryRole": "The subject's does not have any related subjects with the necessary rol to entry: ", "exitLocationNotFound": "The location to exit was not found", "needRoleToEntry": "The subject needs a pending role to enter", "noEntryOrExitUser": "The subject does not have permission to enter or exit", "noEntryOrExitUserLocation": "The subject does not have permission to enter or exit this location", "noCreateLocationBecausePending": "The subject has a pending location to enter, you cannot create a new location", "message_remove": "Are you sure you want to delete ", "message_update": "Are you sure you want to update ", "invalid_shedule_type": "Please select a valid schedule type.", "error_range_dates": "The start time cannot be greater than the end time.", "no_days_selected": "Please select at least one day.", "group_same_name": "A group with that name already exists.", "assignment_same_name": "An assignment with that name already exists.", "category_flow_required": "To create assignments, you must have at least one workflow and one category.", "flow_required": "To create assignments, you must have at least one workflow.", "category_required": "To create assignments, you must have at least one category.", "warning": "Warning", "user_password_error": "User or password is incorrect.", "error_username_password_required": "Username and password are required.", "error_email_password_required": "Email and password are required.", "delete_confirmation_header": "Delete confirmation", "delete_multiple_records": "Are you sure you want to delete the selected records?", "delete_single_record": "Are you sure you want to delete the record:", "page_not_found": "Sorry, the page not found.", "unauthorized": "Sorry, you don't have permission to access this resource.", "widget_error": "There has been an error with the widget.", "tenant_id_not_found": "Tenant ID not found", "konektor_properties_not_found": "Konektor properties not found", "konektor_connection_error": "Error connecting to Konektor", "get_konektor_properties_error": "Error getting Konektor properties", "konektor_properties_error": "Error with Konektor properties", "must_select_reason": "You must select a reason", "password_not_match": "Passwords do not match.", "error_updating_password": "Error trying to update the password.", "password_updated": "Password updated successfully!", "no_cam_detected": "No camera detected", "no_cam_selected": "No camera selected", "no_data_to_save": "There is no data to save.", "no_data_to_search_coincidence": "There is no data to search coincidence.", "subject_inside_time_left": "The subject is inside the facility, the time left is: ", "subject_outside_time_left": "The subject is outside the facility, the time left is: ", "verification_required": "If you want to edit or delete subject data, you must verify yourself with biometrics as a user.", "verification_required_user": "If you want to edit or delete user data, you must verify yourself with biometrics as a user.", "verification_required_data": "If you want to access to edit this data, you must verify yourself with biometrics as a user.", "verification_required_enroll": "If you want to enroll a new subject, you must verify yourself with biometrics as a user.", "subject_verification_required_data": "If you want to access to edit this data, you must verify the subject with biometrics.", "error_getting_access": "Error getting access", "error_login": "Login error", "duplicate_subject_numId": "The ID number is already in use", "duplicate_user_email": "The email is already in use", "duplicate_user_numId_or_email": "The ID number or email is already in use", "error_technology_not_allowed": "The technology is not allowed", "no_techs_available": "No technologies available", "window_used": "This window is already in the flow", "saved_successfully": "Saved successfully!!!", "updated_successfully": "Updated successfully!!!", "removed_successfully": "Removed successfully!!!", "error_retrieving_data": "Error retrieving data", "error_delete": "Error trying to remove", "error_delete_record": "Error trying to remove the record", "error_save": "Error trying to save", "error_save_record": "Error trying to save the record", "error_update": "Error trying to update", "error_update_record": "Error trying to update the record", "success_delete_record": "The record has been deleted successfully", "success_save_record": "The record has been saved successfully", "success_update_record": "The record has been updated successfully", "message_update_data_source": "Are you sure you want to update ", "message_add_parameters": "Are you sure you want to add the new parameters to ", "message_remove_subject_app": "Are you sure you want to remove user's app: ", "api_connection_error": "Error in API connection ", "error_subject_has_user": "The subject has a user associated with it", "error_resetting_password": "There was an error while trying to reset the password and the recovery email could not be sent, please contact your system administrator", "password_reset_email_sent": "An email has been sent to the email provided with instructions on how to reset the password", "error_license": "Error with the license, please contact your system administrator", "error_license_no_access": "Your license does not allow access to this application", "error_license_not_configured_konektor": "The license is not configured in Konektor", "error_cannot_access_without_license": "You cannot access this application without a license, please contact your system administrator", "cannot_delete_subject_user": "The subject cannot be deleted because it has a user associated with it", "cannot_save_subject_user_ds": "The subject cannot be saved because the selected datasource does not allow it", "cannot_create_subject_user_ds": "The subject cannot be created because the selected datasource does not allow it", "cannot_update_subject_user_ds": "The subject cannot be updated because the selected datasource does not allow it", "cannot_delete_subject_user_ds": "The subject cannot be deleted because the selected datasource does not allow it", "ContactAdminCamera": "Check if the camera has permission to be used on this website", "error_widget_url": "Error with the widget URL, please contact your system administrator", "timeout": "The timeout has to be more than 10 sec. and less than 30 min.", "new_theme_added": "New theme added!", "method_added": "Method added!", "mapping_pair_added": "Mapping pair added:", "connection_added": "New API connection added!", "error_unlink_license": "Error trying to unlink the license", "error_license_scope": "The number of licenses is exceeded for the following applications: ", "reduce_num_licenses": "Are you sure you want to reduce the number of licenses for the selected application? This action will also remove applications from the licenses assigned or remove the license if no applications remain.", "passlink_not_available": "PassLink is not available. Please check the connection and try again.", "no_match": "The password doesn't match!", "error_obtaining_subject_apps": "Error trying to get subject applications. Please contact the system administrator.", "adding_credentials": "Adding credentials, please wait.", "no_credentials_to_update": "No credentials to update.", "executing_events": "Executing events, please wait.", "error_updating_credentials": "Error trying to update credentials.", "credentials_updated": "Credentials updated successfully.", "subject_without_apps": "No applications assigned.", "web_not_valid": "Website not valid.", "unable_to_login": "Unable to login.", "konektor_properties_missed": "Some Konektor settings are missing. Please verify and try again.", "confirm_element_not_found": "The confirmation element was not found so the credentials will not be updated.", "no_apps_assigned": "No applications assigned in Pass.", "no_apps_assigned_web": "There are no web applications assigned to your identity for Verázial ID Pass. Please assign a web application in the Pass section of Verázial ID Admin to proceed further.", "no_apps_assigned_passlink": "No applications assigned in PassLink or they don't match with the applications assigned in Pass.", "pageNotFound": "Page not found", "pageNotFoundDescription": "The page you are looking for does not exist.", "unauthorised": "Unauthorised", "unauthorisedDescription": "You are not authorised to access this page.", "touchTheScreen": "Touch the screen", "toStart": "to ", "start": "start", "error_auth_token": "Error retrieving the authentication token", "noTechSelected": "No main biometric technology selected", "noTechSelectedDescription": "Please set a main technology in Konektor.", "noTechsAvailable": "No biometric technologies available", "noTechsAvailableDescription": "There are no biometric technologies available. Please contact the system administrator.", "errorUpdatingTransferUser": "Error updating the transfer authorization user", "errorUpdatingTransferAuth": "Error updating transfer authorization", "errorUpdatingEntryExitAuth": "Error updating entry and exit authorization", "generalErrorTitle": "An error has occurred", "generalErrorDescription": "An unexpected error has ocurred.", "settingsErrorTitle": "Error retrieving the settings", "settingsErrorDescription": "An error occurred while trying to retrieve the settings.", "konektorErrorTitle": "Error with Konektor settings", "widgetSettingsErrorTitle": "Error retrieving the widget's settings", "widgetSettingsErrorDescription": "An error occurred while trying to retrieve the widget's settings.", "widgetURLSettingsErrorTitle": "Error widget's URL settings", "widgetURLSettingsErrorDescription": "An error occurred while trying to utilize the configured widget's URL.", "techErrorTitle": "Biometric technology error", "techErrorDescription": "There are no biometric technologies set.", "techErrorPayedDescription": "There are no contracted biometric technologies set.", "techErrorAllowSearchDescription": "There are identification biometric tecnologies set.", "fingerprintTechErrorTitle": "Fingerprint technology error", "fingerprintTechErrorDescription": "The fingerprint technology is not set.", "fingerprintTechErrorPayedDescription": "The fingerprint technology is not set as a contracted technology.", "fingerprintTechErrorAllowSearchDescription": "The fingerprint technology is not set as an identification technology.", "fingerprintTechErrorAllowVerifyDescription": "The fingerprint technology is not set as a verification technology.", "irisTechErrorTitle": "Iris technology error", "irisTechErrorDescription": "The iris technology is not set.", "irisTechErrorPayedDescription": "The iris technology is not set as a contracted technology.", "irisTechErrorAllowSearchDescription": "The iris technology is not set as an identification technology.", "irisTechErrorAllowVerifyDescription": "The iris technology is not set as a verification technology.", "facialTechErrorTitle": "Face technology error", "facialTechErrorDescription": "The face technology is not set.", "facialTechErrorPayedDescription": "The face technology is not set as a contracted technology.", "facialTechErrorAllowSearchDescription": "The face technology is not set as an identification technology.", "facialTechErrorAllowVerifyDescription": "The face technology is not set as a verification technology.", "geolocationNotSupported": "Geolocation is not supported by this browser.", "dataInconsistencyErrorTitle": "Data inconsistency", "dataInconsistencyErrorDescription": "The identity exists on Verázial ID Server, but not on your biographic server. Please contact your system administrator.", "saveActionErrorTitle": "Error saving the action", "saveActionErrorDescription": "An error occurred while trying to save the action.", "getActualTimeErrorTitle": "Error retrieving the actual time", "getActualTimeErrorDescription": "An error occurred while trying to get the actual time.", "langModifiedSuccess": "Language modified successfully", "abortOperation": "The user cancelled the operation", "searchAssignmentsErrorTitle": "Error searching for the assignments", "searchAssignmentsErrorDescription": "An error occurred while trying to search for the subject assignments.", "searchAssignmentsWarningTitle": "No flow action assignments found", "searchAssignmentsWarningDescription": "No flow action assignments were found for the subject and/or location.", "noActionsAvailableErrorTitle": "No actions available", "noActionsAvailableErrorDescription": "There are no actions available for the subject, device or current schedule.", "noFlowsAssignedErrorTitle": "No flows assigned", "noFlowsAssignedErrorDescription": "There are no flows assigned to the subject, device or current schedule.", "noFlowsAssignedErrorBothDescription": "There are no flows assigned to the subject and location.", "noFlowsAssignedErrorBothScheduleDescription": "There are no flows assigned to the subject, location, and schedule.", "noFlowsAssignedErrorLocationDescription": "There are no flows assigned to the location.", "noFlowsAssignedErrorLocationScheduleDescription": "There are no flows assigned to this location for the current schedule.", "noFlowsAssignedErrorSubjectDescription": "There are no flows assigned to the subject.", "noFlowsAssignedErrorSubjectScheduleDescription": "There are no flows assigned to the subject for the current schedule.", "noFlowsAssignedErrorSubjectLocationDescription": "There are no flows assigned to the subject and location.", "noFlowsAssignedErrorSubjectLocationScheduleDescription": "There are no flows assigned to the subject and location for the current schedule.", "noFlowsInAssignmentTitle": "No flows in assignment", "noFlowsInAssignmentBothDescription": "There are no flows in the assignment for the subject and location.", "noFlowsInAssignmentLocationDescription": "There are no flows in the assignment for the location.", "noFlowsInAssignmentSubjectDescription": "There are no flows in the assignment for the subject.", "noValidFlowsFoundTitle": "No valid flows found", "noValidFlowsFoundDescription": "No valid flows were found for the subject, device or current schedule.", "noValidFlowsFoundBothDescription": "There are no valid flows in the assignment for the subject and location.", "noValidFlowsFoundBothScheduleDescription": "There are no valid flows assigned to the subject, location, and schedule.", "noValidFlowsFoundLocationDescription": "There are no valid flows for this location.", "noValidFlowsFoundLocationScheduleDescription": "There are no flows assigned to this location and schedule.", "noValidFlowsFoundSubjectDescription": "There are no valid flows in the assignment for the subject.", "noValidFlowsFoundSubjectScheduleDescription": "There are no valid flows in the assignment for the subject on the current schedule.", "noValidFlowsFoundSubjectLocationDescription": "There are no valid flows in the assignment for the subject and location.", "noValidFlowsFoundSubjectLocationScheduleDescription": "There are no valid flows in the assignment for the subject and location on the current schedule.", "noValidAssignmentsFoundTitle": "No valid assignments found", "noValidAssignmentsFoundDescription": "There are no valid assignments for the subject and/or location.", "noValidAssignmentFoundBothDescription": "There are no valid assignments for the subject and location.", "noValidAssignmentFoundLocationDescription": "There are no valid assignments for the location.", "noValidAssignmentFoundSubjectDescription": "There are no valid assignments for the subject.", "fireKonektorRelayErrorTitle": "Error firing the Konektor relay", "fireKonektorRelayErrorDescription": "An error occurred while trying to fire the Konektor relay.", "noValidFlowsErrorTitle": "No valid flows found", "noValidFlowsErrorDescription": "No valid flows were found for the subject, device or current schedule.", "authServerConnectionErrorTitle": "Error connecting to the authentication server", "authServerConnectionErrorDescription": "An error occurred while trying to connect to the authentication server.", "managerSettingsErrorTitle": "Error retrieving the Manager settings", "managerSettingsErrorDescription": "An error occurred while trying to retrieve the Manager settings.", "systemSettingsErrorTitle": "Error retrieving the system settings", "systemSettingsErrorDescription": "An error occurred while trying to retrieve the system settings.", "systemSettingsErrorNotFound": "System settings not found", "enterNumID": "Enter the ID number", "invalidNumId": "Invalid ID number", "licenseErrorDescription": "Error with the license, please contact your system administrator", "no_data_found": "No data available", "no_data": "No data", "DisabledLicenseTitle": "License disabled", "NoLicenseMessage": "No active license found for this software. Please contact your system administrator", "error_change_tenant": "There was an error changing the tenant", "user_created_success": "User created successfully", "a_subject_was_created_based_on_the_user": "User created successfully. A subject was created based on the user", "location_updated": "Location updated successfully", "location_updated_error": "Error updating location", "location_created": "Location created successfully", "location_created_error": "Error creating location", "location_deleted": "Location deleted successfully", "location_deleted_error": "Error deleting location", "cant_delete_actual_location": "You cannot delete the actual location", "delete_location": "Are you sure you want to delete the location: ", "no_delete_location": "The location cannot be deleted because it is occupied by a subject", "action_created": "Action created successfully", "action_created_error": "Error creating action", "error_locations": "Error with locations", "error_origin_destiny_locations": "The origin and destination locations cannot be the same", "error_dates": "Error with dates", "error_departure_arrival_dates": "The departure date cannot be greater than the arrival date", "error_list_of_prisoners": "Error with the list of prisoners", "error_at_least_one_subject": "You must select at least one subject", "error_list_of_responsible_personel": "Error with the list of responsible personnel", "error_transfer_authorization": "Error with the transfer authorization", "all_required_fields_details_prisoners_responsible_personnel": "All required fields must be filled, at least one prisoner subject and one responsible personnel subject must be selected", "success_transfer_authorization_created": "Transfer authorization created successfully", "success_transfer_authorization_updated": "Transfer authorization updated successfully", "error_transfer_authorization_updated": "Error updating transfer authorization", "error_must_select_cancel_reason": "You must select a cancellation reason", "error_subject_not_allowed_to_sign": "The subject does not have the appropriate role to sign this action with biometrics", "error_start_end_dates": "The start date cannot be greater than the end date", "error_entry_exit_authorization": "Error with the entry/exit authorization", "success_entry_exit_authorization_created": "Entry/exit authorization created successfully", "success_entry_exit_authorization_updated": "Entry/exit authorization updated successfully", "duplicate_nif": "The CID is already in use", "error_current_tenant_not_same_as_configured_in_konektor": "The current tenant is not the same as the one configured in Konektor", "error_jobNotActive": "The job is not active", "error_jobExecutionFailed": "The job execution failed, check the logs on the server", "success_jobExecutionSuccess": "The job was executed successfully", "error_cronServiceSchedulerInit": "Error initializing the cron service scheduler, check the logs on the server", "cronServiceSchedulerInitSuccess": "Cron service scheduler initialized successfully", "success_jobExecutionScheduled": "The job was scheduled successfully", "error_jobExecutionScheduled": "Error scheduling the job, check the logs on the server", "success_removingJobScheduled": "The job was removed from the scheduler successfully", "error_removingJobScheduled": "Error removing the job from the scheduler, check the logs on the server", "error_deleting_roles": "Error deleting roles", "error_deleting_profiles": "Error deleting profiles", "error_adding_roles": "Error adding roles", "error_adding_profiles": "Error adding profiles", "error_retrieving_roles": "Error retrieving roles", "error_retrieving_profiles": "Error retrieving profiles", "error_creating_user": "Error creating user", "error_creating_subject": "Error creating subject", "error_updating_user": "Error updating user", "error_updating_subject": "Error updating subject", "error_deleting_user": "Error deleting user", "error_deleting_subject": "Error deleting subject", "error_uploading_image": "Error uploading image", "error_downloading_image": "Error downloading image", "error_removing_image": "Error removing image", "error_saving_extended_biographic_data": "Error saving extended biographic data", "error_obtaining_extended_biographic_data": "Error obtaining extended biographic data", "error_retrieving_criminal_cases": "Error retrieving criminal cases", "error_retrieving_users": "Error retrieving users", "error_retrieving_subjects": "Error retrieving subjects", "error_retrieving_user": "Error retrieving the user", "error_retrieving_subject": "Error retrieving the subject", "error_retrieving_user_subject_data": "Error retrieving the user's subject data", "error_processing_subject_deletion": "Error processing subject deletion", "error_retrieving_system_report": "Error retrieving system report", "error_retrieving_biometric_sample_count": "Error retrieving the number of biometric samples", "error_retrieving_license_report": "Error retrieving license report", "error_retrieving_number_of_users": "Error retrieving the number of users", "error_retrieving_number_of_subjects": "Error retrieving the number of subjects", "error_retrieving_system_settings": "Error retrieving system settings", "error_no_system_settings_found_for_application": "No system settings found for the application", "error_retrieving_konektor_properties": "Error retrieving Konektor properties", "error_deleting_related_subjects": "Error deleting related subjects", "error_deleting_entry_exit_auth": "Error deleting entry/exit authorization", "error_updating_location": "Error updating location", "error_creating_tenant": "Error creating tenant", "error_updating_tenant": "Error updating tenant", "error_deleting_tenant": "Error deleting tenant", "error_creating_role_profile": "Error creating role/profile", "error_updating_role_profile": "Error updating role/profile", "error_deleting_role_profile": "Error deleting role/profile", "error_retrieving_role_profile": "Error retrieving role/profile", "error_deleting_transfer_auth": "Error deleting transfer authorization", "error_deleting_belongings": "Error deleting belongings", "error_deleting_auth_schedule": "Error deleting authorization schedule", "error_deleting_category": "Error deleting category", "error_updating_task_flow": "Error updating task flow", "error_deleting_judicial_file": "Error deleting judicial file", "locations_available_in_a_future_date": "No locations available, but there are {var} locations available in a future date: {1}", "location_available_in_a_future_date": "No locations available, but there is 1 location available in a future date: {1}", "error_translation_already_exists_for": "The translation already exists for the selected language {0}", "error_translation_record_already_exists_for_key": "The translation record already exists for the key {0}", "user_sessions_exceeded_description": "The user has exceeded the maximum number of sessions allowed. The maximum sessions allowed is {0}.", "user_blocked_description": "The user is blocked. The user will be unlocked after {0}", "user_remaining_attempts_description": "The are {0} attempts remaining before the user is blocked.", "user_lock_on_next_attempt": "The user will be blocked if the next access attempt is incorrect.", "file_size_exceeded": "{0}: Invalid file size", "file_size_exceeded_detail": "The file size exceeds the maximum allowed size of {0}", "invalid_file_type": "{0}: Invalid file type", "invalid_file_type_detail": "The allowed file types are {0}", "invalid_file_limit_detail": "Limit is {0} files at most", "invalid_file_limit": "Maximum number of files exceeded", "fileUploadedSuccess": "File {0} uploaded successfully", "fileUploadedError": "Error uploading file {0}", "previewNotAvailable": "Preview not available", "downloadNotAvailable": "Download not available", "fileDownloadedSuccess": "File {0} downloaded successfully", "fileContentNotFound": "File content not found", "error_downloading_file": "Error downloading file", "data_update_sent": "Data updated locally and sent to the target application.", "error_creating_case": "Error creating criminal case", "error_updating_case": "Error updating criminal case", "error_deleting_case": "Error deleting criminal case", "invalid_max_time": "The maximum time cannot be 00:00", "noValidSampleTypeSelected": "Please select a valid sample type for each sample.", "error_enrolling_subject": "Unable to enroll subject", "duplicate_sample": "<PERSON><PERSON> already enrolled in a different subject", "BAD_FILTER": "Bad filter", "EXTRACTION_UNKNOWN": "Extraction unknown", "EXTRACTION_NOT_ENOUGH_QUALITY": "The sample does not meet the quality requirements", "UNKNOWN": "Unknown error", "NO_LICENSE": "No license available", "must_contain_at_least_one_mapping": "Must contain at least one mapping", "index_already_used": "The index is already used", "type_already_used": "The type is already used", "error_retrieving_system_user_profile": "Error retrieving the system user profile", "cannot_delete_self": "You cannot delete yourself", "error_searching_coincidence_type_not_defined": "Error retrieving coincidence. The coincidence type is not defined", "user_must_be_verified": "You must verify yourself with biometrics.", "": ""}, "options": {"true": "Yes", "false": "No", "male": "Male", "female": "Female", "M": "Male", "F": "Female"}, "reasons": {"biometric_auth": "Biometric authentication", "biometric_id": "Biometric identification", "biometric_login": "Biometric login", "pass_login": "User/password login", "pin_login": "Identification with ID", "key_login": "Login with one-time password", "no_dev": "Device not detected", "new_subject": "Add new subject", "new_sample": "Add biometric sample", "new_picture": "Add profile picture", "update_pass": "Update password"}, "role": {"roles": "Roles", "level": "Level", "new_role": "New Role", "new_profile_role": "New Profile/Role", "general": "General", "accesses": "Accesses", "selected_roles": "Selected roles", "select_role": "Select role", "change_role": "Change role", "select_as_default_role": "Select as default role", "update_role": "Update role", "update_profile_role": "Update Profile/Role", "no_roles_available": "No roles available"}, "role_names": {"SYSTEM_USER": "System User"}, "pass_application": {"applications": "Applications", "application_flows": "Application Processes", "technology": "Technology", "application_type": "Type", "data_source": "Datasource", "new_application": "New application", "new_application_flow": "New process", "save_application": "Save Application", "save_application_flow": "Save Application Process", "full_path": "Path or URL", "the_window": "the window", "new_window": "New window", "add_window": "Add window", "application": "Application", "no_windows": "No windows", "target": "Target", "edit_window": "Edit Window", "window": "Window", "window_components": "Window Components", "new_component": "New component", "attribute_name": "Attribute name", "component_type": "Component type", "trigger_order": "Trigger order", "position": "Position", "event": "Event", "display_component_user": "Display component to the user?", "update_application": "Update Application", "update_application_flow": "Update Application Process", "remove_application": "Remove Application", "remove_application_flow": "Remove Application Process", "no_applications_available": "No applications available", "authentication": "Authentication", "process": "Process", "update_credentials": "Update credentials", "appRegistry": "Application", "flow_type": "Flow Type"}, "pass_datasource": {"datasources": "Datasources", "new_datasource": "New datasource", "method": "Method/URL", "parameters": "Parameters", "update_datasource": "Update Datasource", "add_datasource": "Add Datasource", "datasource": "Datasource", "add_parameter": "Add parameter", "parameter": "Parameter", "add_parameters": "Add Parameters", "remove_data_source": "Remove Datasource", "no_datasource_available": "No datasources available"}, "pass_assigment": {"application_assigment": "Applications Assignment", "Application_identifier": "Application ID", "authorization_id": "Authorization ID", "application": "Application", "application_flow": "Application Processes", "application_flow_name": "Process Name", "connection_type": "Connection Type", "can_user_update": "Update Password", "initialised": "Initialised", "host": "Host", "subject_application": "Subject's Applications", "credentials": "Credentials", "parameter": "Parameter", "value": "Value", "fill_credential": "Add credentials and data", "select_apps": "Please select an application.", "select_apps_flow": "Please select an application process.", "allow_user_update_password": "Allow the user to update his/her password.", "must_user_update_credentials": "Must credentials be initialised?", "host_name": "IP Address/Host name", "datagrip_row": "Enter the number of table row.", "option_dropdown": "Enter the text of the option to select in the dropdown.", "option_list": "Enter the text of the option to select in the list.", "window": "Window", "application_credential": "Application and Credentials", "host_name_required": "IP Address/Host name is required"}, "headers": {"contracted_bio_tech": "Contracted Biometric Technologies", "after_searching_by_id": "After Searching By ID, Allow", "allow_searching_by": "Allow Searching By", "tech_required_enroll": "Technology Required to Enroll", "basic_bio_fields": "Basic Biographical Fields", "min_quality_samples": "Minimum Quality of Samples", "avg_quality_samples": "Average Quality of Samples", "allow_verify_by": "Allow Verify By", "num_iris_one_n": "Num. Of Iris In 1:N", "sensors_capacity": "Sensors Capacity", "num_fp_one_n": "Num. Of Fingerprints In 1:N", "num_fp_roll_one_n": "Rolled Fingerprints In 1:N", "num_palm_one_n": "Palms In 1:N", "min_matching_fp": "Minimum Matching Fingerprints", "reason_uncollect_samples": "Reasons For Uncollectable Samples", "min_iris_match": "Minimum Iris Match", "one_n_when_enrolling": "1:N When Enrolling", "profile_type": "Profile Type", "locations": "Locations", "segmented_search": "Segmented Search", "enroller_session_timeout": "Enroller Session Timeout (ms)", "licences": "Licenses", "licenses": "Licenses", "access_key": "Access Key", "external_bio_server": "External Biographical Server (Legacy)", "config_access": "Configuration Access", "access_action_reg": "Server Actions", "templates_info": "Information about Templates", "verazial_id_server": "Verázial ID Server", "verazial_id_modules": "Verázial ID Modules", "external_servers": "External Servers", "biometric_server": "Biometric Server", "biografic_server": "Biografic Server", "storage_server": "Storage Server", "clock_server": "Clock Server", "extended_bio_fields": "Extended Biografic Fields", "user": "Users", "roles": "Roles", "asign_modules": "Module Assignment", "modules": "<PERSON><PERSON><PERSON>", "role": "Rol", "external_guess_fields": "External Guess Fields", "external_employee_fields": "External Employee Fields", "tenant": "Tenant", "user_verification_userpass": "User login with basic authentication", "user_verification_biometric": "User login with biometrics", "password_recovery_token_expiration": "Password Recovery Token Expiration", "password_expiration": "Password Expiration", "biographic_data_management_reasons": "Reasons - Biographic Data Management", "biometric_samples_management_reasons": "Reasons - Biometric Samples Management", "config_management_reasons": "Reasons - Configuration Management", "password_management_reasons": "Reasons - Password Management", "location_management_reasons": "Reasons - Location Management", "tenant_license_management_reasons": "Reasons - Tenant's License Management", "role_management_reasons": "Reasons - Role Management", "export_reports_management_reasons": "Reasons - Export Reports Management", "user_management_reasons": "Reasons - User Management", "pass_app_management_reasons": "Reasons - Pass Application Management", "concurrent_sessions": "Concurrent Sessions", "audit_trail_config": "Audit Trail Configuration", "intern_fields": "<PERSON>n <PERSON>", "external_provider_fields": "External Provider Fields", "physical_fields": "Physical Fields", "visiting_time_visitors": "Visitor Visit Time", "supplier_visit_time": "Supplier Visit Time", "reasons_exit": "Reasons Exit", "allow_entry_exit": "Allow Entry and Exit", "update_password": "Update Password", "modify": "Modify", "delete": "Remove", "widget_options": "Widget Access Configuration", "subjectTabsConfig": "Subject Page Tabs Configuration", "debugMode": "Debug Mode", "streamingSamples": "Streaming Samples", "general": "General", "general_widget": "Widget Configurations", "general_clock": "Clock Configurations", "general_pass": "Pass Configurations", "segment": "Segments", "devices": "Devices", "security": "Security", "verification": "Verification", "application_login": "Application Login", "audit_trail": "Audit Trail", "page_not_found": "Page not Found", "unauthorized": "Forbidden", "theme": "Theme", "subject_data_origen": "Subject Data Origen", "api_gateway": "API Gateway", "actions_extra_data": "Actions - Extra Data", "new_license": "New license", "new_api_connection": "New API Connection", "update_api_connection": "Update API Connection", "confirm_reduce_num_licenses": "Reduce number of licenses", "update_ldap_connection": "Update LDAP Connection", "new_ldap_connection": "New LDAP Connection", "tenant_licenses": "Tenant's Licenses", "user_verification": "User Verification", "client_data_source": "Client Datasource", "applications": "Applications", "application": "Application", "application_window": "Application Window", "window_components": "Window's Components", "remove_component": "Remove Component", "edit_window": "Edit Window", "save_window": "Save Window", "remove_window": "Remove Window", "save_application": "Save Application", "remove_application": "Remove Application", "update_application": "Update Application", "report_error": "Report an Error", "data_sources": "Datasources", "remove_data_source": "Remove Datasource", "update_data_source": "Update Datasource", "update_parameters": "Update Parameters", "add_parameters": "Add Parameters", "create_data_source": "Create Datasource", "assign_application": "Assign Application", "select_language": "Select a language", "selectLanguage": "Select a language", "selectFlow": "Select a flow", "flowSelection": "Flow Selection", "catalogs": "Catalogs", "passwords_complexity": "Passwords Complexity", "prisonsSettings": "Prisons Settings", "belongings": "Belongings", "belonging": "Belonging", "belonging_record": "Belonging Record", "judicial_files": "Judicial Files", "judicial_file": "Judicial File", "prisons_visits_list": "Prisons Visits List", "prisons_authorization_schedules": "Prisons Authorization Schedules", "custom_partner_logo": "Custom Logo", "cron_service": "Cron Service", "cron_service_config": "Cron Service Configuration", "cron_service_jobs": "Cron Service Jobs", "threshold_configs": "Threshold Configurations", "inputTextAreaThreshold": "Input text area threshold", "prometheusCredentials": "Prometheus Credentials", "authentication": "Authentication", "pass_app_flow_types": "Pass - Application Flow Types", "criminalistics_settings": "Criminalistics Settings", "services": "Services", "application_configs": "Application Configurations", "pass_settings": "Pass Settings", "clock_settings": "Clock Settings"}, "content": {"username": "Username", "password": "Password", "fingerprint": "Fingerprint", "face": "Face", "facial": "Face", "iris": "Iris", "primary": "Primary", "secondary": "Secondary", "name": "Name", "key": "Key", "value": "Value", "drop_down": "Drop-down", "search": "Search", "date": "Date", "show_table": "Show table", "option": "Option", "known_position": "Known position", "no_match_search_db": "Without match, search in the whole DB", "search_two_fp": "Search for 2 fingerprints", "search_four_fp": "Search for 4 fingerprints", "reason": "Reason", "search_two_iris": "Search for 2 iris", "take_one_iris": "Take 1 iris", "take_two_iris": "Take 2 iris", "min_one_iris": "Minimum 1 iris", "min_two_iris": "Minimum 2 iris", "min_one_fp": "Minimum 1 fingerprint", "min_two_fp": "Minimum 2 fingerprints", "min_three_fp": "Minimum 3 fingerprints", "min_four_fp": "Minimum 4 fingerprints", "take_one_fp": "Take 1 fingerprint", "take_two_fp": "Take 2 fingerprints", "take_four_fp": "Take 4 fingerprints", "add_profile_type": "Add profile type", "enroller": "Enroller", "employee": "Employee", "intern": "Intern", "code": "Code", "default_profile_type": "Default profile type", "location": "Location", "segment": "Segment", "relation_location": "Relation with localization", "device": "<PERSON><PERSON>", "relation_segment": "Relation with segment", "segmented_search": "Segmented search", "num_available_lic": "Number of available licenses", "show_licenses": "Show licenses", "modify_key": "Modify key", "available": "Available", "selected": "Selected", "available_access_permissions": "Available access permissions", "selected_access_permissions": "Selected access permissions", "url": "URL", "delete_entity": "Delete entity", "edit_entity": "Edit entity", "related_with": "Related with", "profile": "Profile", "id_number": "ID number", "nothing": "None", "role": "Role", "roles": "Roles", "module": "<PERSON><PERSON><PERSON><PERSON>", "modules": "<PERSON><PERSON><PERSON><PERSON>", "show_roles": "Show roles", "show_users": "Show users", "show_assigments": "Show assignments", "left_iris": "Left iris", "right_iris": "Right iris", "left_hand": "Left hand", "right_hand": "Right hand", "tenant": "Tenant", "select": "Select", "enable_edit": "Enable edit", "errorTitle": "Error", "successMessage": "Saved or removed successfully!", "successTitle": "Result", "download": "Download", "tenant_name": "Tenant name", "show_tenants": "Show tenants", "version": "Version", "description": "Description", "new_version": "New version", "show_access": "Show access", "user_lock_time": "Lock Time", "expiration_time": "Expiration time", "user_num_attempts": "Number of Attempts", "is_enable_lock_user": "Enable lock user", "user_lock_time_by": "Lock Time in", "expiration_time_in": "Expiration time in", "second": "Seconds", "minute": "Minutes", "hour": "Hours", "day": "Days", "number_sessions": "Number of sessions", "action_name": "Action name", "is_enable": "Is enable?", "new_action": "New Action", "ecualize_images": "Equalise images", "identify_with_template": "Use templates when searching", "copy_data": "Copy data", "duplicate_version": "This product is already in the DB.", "file_not_supported": "File not supported. The files allowed are .zip, .exe and .msi.", "successfully_removed": "Successfully removed", "number": "Number", "allow": "Allow", "device_used": "Devices used", "selectColumns": "Select columns", "columnsSelected": "Columns selected", "selectedColumns": "Selected columns", "biometricSearch": "Biometric search", "Identification": "Identification", "subjectIdentification": "Subject identification", "userIdentification": "User identification", "schedule": "Schedule", "start": "Start", "end": "End", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "subject": "Subject", "subject_profile": "Subject profile", "user": "User", "user_role": "User role", "monday_to_friday": "Monday to Friday", "customised": "Customised", "everyday": "Every day", "type": "Type", "to": "to", "level": "Level", "must_update": "User must change password at next logon.", "email": "Email", "repeat_password": "Repeat password", "welcome": "Welcome", "lastAccess": "Last access", "select_reason": "Select reason", "new_password": "New password", "active": "Active", "inactive": "Inactive", "under_verification": "Under verification", "documents": "Documents", "profiles": "Profiles", "tattoos": "Tattoos", "scars": "Scars", "select_camera": "Select camera", "enterNumID": "Enter the ID number", "replace_image": "Replace", "general": "General", "server": "Server", "hardDrive": "Hard Drive", "ram": "RAM", "biometricServer": "Biometric Server", "subjectsDB": "Subjects DB", "numSubjects": "Number of subjects", "usersDB": "Users DB", "numUsers": "Number of users", "numRecords": "Number of records", "status": "Status", "noDataAvailable": "No data available", "noPhotosAdded": "No photos added", "showing": "Showing", "of": "of", "records": "records", "requiredFields": "Required fields", "forgot_password": "Forgot password", "oneTimePasswordVerification": "One-time password verification", "enterAccessKey": "Enter the access key", "accessKey": "Access key", "createSubjectToContinue": "Create the subject to continue", "takePhoto": "Take photo", "changePhoto": "Change photo", "rolledFingerprint": "Rolled fingerprint", "palm": "Palm", "minimumCoincidence": "Minimum Coincidence", "match": "Match", "enroll": "Enroll", "verify": "Verify", "error_title": "Error", "error_message_required_fields": "Please, fill in the required fields.", "error_duplicate_location": "The location already exists.", "error_duplicate_segment": "The segment already exists.", "error_duplicate_device": "The device already exists.", "error_duplicate_option": "The option already exists.", "some_duplicate_option": "Some option already exist.", "success_message": "Processed successfully!", "success_title": "Result", "try_again": "Try again", "auto_enroll": "Auto enroll", "option_not_valid": "Option no valid", "no_reasons_configured": "No reasons configured", "data_inconsistent": "Data inconsistent. Please, refresh the page.", "reason_change": "Reason to modify", "showAdditionalTabs": "Show Aditional Tabs", "restrictTabToSpecificRoles": "Restrict Tab to Specific Profile", "specificRoles": "Specific Profiles", "showPrisonTab": "Show Prison Tab", "showProfilePictureTab": "Show Profile Picture History Tab", "showExtendedBioFieldsTab": "Show Extended Bio Fields Tab", "showPhysicalDataTab": "Show Physical Data Tab", "showRelatedSubjectsTab": "Show Related Subjects Tab", "showLocationsTab": "Show Locations Tab", "showEntriesExitsTab": "Show Entries and Exits Tab", "showEntryExitAuthorizationsTab": "Show Entry and Exit Authorizations Tab", "enableDebugMode": "Enable Debug Mode", "autoDelete": "Automatic Biometric Data Deletion", "api_credentials": "API Credentials", "role_assigned": "The following roles have already been assigned to some users:", "confirmation_continue": "Are you sure you want to continue?", "tenant_not_valid": "Tenant not valid.", "confirmation": "Confirmation", "show_actions": "Show actions", "field_required": "Field required", "options": "Options", "capture_one_iris": "Capture 1 iris", "capture_two_iris": "Capture 2 iris", "one_fingerprint": "1 fingerprint", "two_fingerprints": "2 fingerprints", "three_fingerprints": "3 fingerprints", "four_fingerprints": "4 fingerprints", "timeoutGeneral": "General Timeout (s.)", "timeoutInactivity": "Inactivity Timeout (s.)", "timeoutCapture": "Capture Timeout (s.)", "timeoutStaticMessage": "Static Message Timeout (s.)", "timeoutNotification": "Notification Timeout (s.)", "timeoutRequestsServer": "Server Requests Timeout (s.)", "faceWidthMin": "<PERSON><PERSON><PERSON> (px.)", "matchAutoFaceCapture": "Automatic Face Capture (Verázial-ID Match)", "matchAutoFaceCaptureTime": "Automatic Face Capture Time (s.)", "autoPalmCapture": "Automatic Palm Capture", "showSuccess": "Wait for success message", "autoPalmCaptureTime": "Automatic Palm Capture Time (s.)", "minQualityFile": "Use minimum quality for file samples", "matchAutoOn": "Auto On (Verázial-ID Match)", "language": "Language", "languageForAlerts": "Language for alerts", "logLevel": "Log Level", "checkWithKonektor": "Check with Konektor", "version1": "Version 1.0", "sipCompatibility": "SIP Compatibility", "admin_profile_type": "Admin Profile", "enroller_profile_type": "Enroller Profile", "show_fields": "Show fields", "show_data": "Show data", "show_jobs": "Show jobs", "show_field_groups": "Show field groups", "extended_fields": "Extended fields", "left_little": "Left little finger", "left_ring": "Left ring finger", "left_middle": "Left middle finger", "left_fore": "Left index finger", "left_thumb": "Left thumb", "right_thumb": "Right thumb", "right_fore": "Right index finger", "right_middle": "Right middle finger", "right_ring": "Right ring finger", "right_little": "Right little finger", "left_little_ring": "Left little and ring fingers", "left_middle_fore": "Left middle and index fingers", "thumbs": "Thumbs", "right_middle_fore": "Right middle and index fingers", "right_little_ring": "Right little and ring fingers", "left_upper_palm": "Left upper palm", "left_lower_palm": "Left lower palm", "left_writer_palm": "Left writers' palm", "right_upper_palm": "Right upper palm", "right_lower_palm": "Right lower palm", "right_writer_palm": "Right writers' palm", "second_search": "2nd search", "related_to": "Related to", "values": "Values", "delete_title": "Delete", "delete_license": "Are you sure you want to delete the selected license(s)? This action will also remove all the licenses assigned.", "installer": "Installer", "upload": "File", "delete_application": "Are you sure you want to delete the selected application?", "delete_confirmation": "Are you sure you want to delete the selected item?", "getSubjectInfoEndpoint": "Subject Info Endpoint", "getActionsInfoEndpoint": "Actions Info Endpoint", "mainStrongBioTech": "Main Biometric Technology", "show_reasons": "Show reasons", "show_profiles": "Show profiles", "show_segments": "Show segments", "guid": "GUID", "serial_number": "Serial Num.", "mac": "MAC", "ip": "IP", "created_at": "Created at", "show_locations": "Show locations", "show_relations": "Show relationships", "show_devices": "Show devices", "show_used_devices": "Show used devices", "relation_add": "Relation added", "relations_add": "Relations added", "cannot_remove_device_used": "The device cannot be removed because it is being used in some license", "cannot_remove_segment_used": "The segment cannot be removed because it is being used in some license", "cannot_remove_location_used": "The location cannot be removed because it is being used in some license", "devices": "Devices", "segments": "Segments", "relationships": "Relationships", "field_type": "Field type", "field_already_exists": "Field already exists", "parameter": "Parameter", "must_not_contain_spaces_or_special_characters": "Must not contain spaces or special characters", "input": "Input", "min_characters": "Min. number of characters", "must_be_less_than_max": "Must be less than the maximum", "max_characters": "Max. number of characters", "must_be_greater_than_min_and_0": "Must be greater than the minimum and 0", "required": "Required", "dropdown": "Dropdown", "at_least_one_option_required": "At least one option is required", "option_already_exists": "Option already exists", "toggle": "Toggle", "api_gateway": "API Gateway", "modify_api_gateway": "Modify API Gateway", "theme": "Theme", "default_theme": "Default theme", "show_themes": "Show themes", "data_origen": "Data origen", "ldap_host": "LDAP host", "ldap_port": "LDAP port", "bind_dn": "Bind DN", "search_base": "Search base", "local": "Local", "external_api": "External API", "ldap": "LDAP", "admin_user_guid": "Admin user GUID", "applied_to": "Applied to", "both": "Both", "criminology": "Show samples images", "nist_score": "NIST score", "other": "Other", "assigned": "Assigned", "used": "Used", "used_by_application": "By application", "maintenance": "Maintenance", "appsStatus": "Apps Status", "mServiceStatus": "Micro services status", "secuential_enroll": "Sequential enroll", "quality_thumbnail": "Thumbnail quality", "config_quality_thumbnail": "Modify thumbnail quality", "config_size_thumbnail": "Modify maximum thumbnail size", "size_thumbnail": "Maximum thumbnail size", "collapse_all": "Collapse All", "expand_all": "Expand All", "total_licenses": "Total Licenses", "licenses": "Licenses", "application": "Application", "license": "License", "enable": "Enable", "last_used": "Last used", "available_licenses": "licenses available: ", "first_used": "First used", "updated_at": "Update at", "delete_single_license": "Are you sure you want to delete the selected license?", "delete_multiple_license": "Are you sure you want to delete the selected licenses?", "user_default_datasource": "Default users datasource", "subject_default_datasource": "Default subjects datasource", "show_api_connections": "Show API connections", "show_ldap_connections": "Show LDAP connections", "id": "ID", "api_token": "API Token", "admin_user_object_guid": "User Admin ID", "admin_user_local_role_id": "User Admin Role ID", "api_methods": "API methods", "connection_name": "Name", "allow_updating": "Update", "allow_deleting": "Delete", "allow_creating": "Create", "allow_reading": "Read", "is_active": "Active", "user_subject_field_id": "Index field", "credentials": "Credentials", "method": "Method", "action": "Action", "header_parameters_mapping": "Header parameters mapping", "api_request_mapping": "Request parameters mapping", "api_response_mapping": "Response parameters mapping", "source": "Source", "target": "Target", "HTTP_GET": "GET", "HTTP_POST": "POST", "HTTP_PUT": "PUT", "HTTP_DELETE": "DELETE", "API_READ": "READ", "API_WRITE": "WRITE", "API_UPDATE": "UPDATE", "API_CREATE": "CREATE", "API_DELETE": "DELETE", "info": "Information", "show_methods": "Show methods", "local_method_name": "Local method name", "camerasFromKonektor": "Cameras from Konektor", "autoOnWidget": "Auto On (Widget)", "autoCaptureWidget": "Facial Auto Capture (Widget)", "autoCaptureTimeFacialWidget": "Facial Auto Capture Time (s.) (Widget)", "readingWaitTimePass": "Reading Wait Time (s.)", "request_json_string": "Request (JSON format)", "response_json_string": "Response (JSON format)", "licenses_used": "Licenses used", "verazial_loading": "Verázial ID Pass is starting ....", "select_tech": "Select a technology", "dear": "Dear", "select_app": "Please select the application process that you want to open.", "select_process": "Please select the process that you want to execute.", "success_login": "Identity found", "error_login": "Identity not found", "loading_apps": "Loading Applications", "technology": "Technology", "application_type": "Application type", "data_source_type": "Source Type", "data_source": "Datasource", "attribute_name": "Attribute name", "component_type": "Component type", "position": "Position", "event": "Event", "saved_successfully": "Saved successfully!!!", "updated_successfully": "Updated successfully!!!", "removed_successfully": "Removed successfully!!!", "select_apps": "Please select an application.", "full_path": "Application path", "display_name": "Display name", "credentials_not_found": "Credentials not found.", "window_title": "Window title", "window": "Window", "trigger_order": "Trigger Order", "first_name": "First Name", "second_name": "Second Name", "last_name": "Last Name", "connection_type": "Connection Type", "subjects": "Subjects", "subject_application": "Subject Applications", "application_name": "Application Name", "application_information": "Application Information", "application_identifier": "Application Identifier", "host": "Host", "fill_credential": "Add credentials and data", "fields_not_valid": "Fields not valid", "order": "Order", "loading_user_info": "Loading user information...", "loading_app_data": "Loading application data...", "loading_cred_ext": "Loading credentials - external...", "loading_cred_int": "Loading credentials - internal...", "can_user_update": "Update password?", "method_not_implemented": "Method not implemented", "update_password": "Update Password", "password_requirements": "Password requirements", "password_req_1": "12 characters", "password_req_2": "1 UPPER case character", "password_req_3": "1 LOWER case character", "password_req_4": "1 special symbol", "password_req_5": "1 numeric value", "confirm_change_password": "Are you sure you want to change your password?", "update_credentials": "Update credentials", "admin_application": "Admin Application", "user_application": "User Application", "host_name": "IP Address/Host name", "administration": "Administration", "couldnt_connect_server": "Could not connect to server.", "lastname": "Last Names", "num_id": "ID number", "display_component_user": "Display component to the user?", "show_component": "Show component", "credentials_updated": "Credentials updated successfully!", "credentials_not_updated": "Credentials couldn't be updated.", "select_user": "Please select at least one user.", "restarting_pass": "Verázial ID Pass will be reloaded in a few seconds.", "must_user_update_credentials": "Must credentials be initialised?", "no_applications_available": "No applications available.", "apps_available": "Applications available", "windows_available": "Windows available", "drag_drop": "Drag and drop ", "the_window": "the window", "message_remove": "Are you sure you want to delete ", "window_exist": "This window already exist.", "publish": "Publish", "published": "Published", "unpublished": "Unpublish", "no_published": "No published", "window_used": "This window is already in the flow", "error_delete": "Error trying to remove", "error_save": "Error trying to save", "error_update": "Error trying to update", "error_web_app": "Web Login and digital sign are only allow using Pass Web Extension.", "user_not_found": "User not found in Admin.", "report_login_error": "Report a login error", "observation": "Observation", "no_datasource_available": "No datasources availables", "edit": "Edit", "parameters": "Parameters", "message_update_data_source": "Are you sure you want to update ", "message_add_parameters": "Are you sure you want to add the new parameters to ", "initialised": "Initialised?", "please_wait": "Please wait", "is_starting": "is starting", "allow_user_update_password": "Allow the user to update his/her password", "input_user_id": "Write your ID number", "min_lowercase": "Must contain at least 1 lowercase letter", "min_uppercase": "Must contain at least 1 uppercase letter", "min_digits": "Must contain at least 1 digit", "min_special_char": "Must contain at least 1 special character", "API_TOKEN": "API Token", "API_ENDPOINT_PARAMETER": "API Endpoint Parameter", "API_USERNAME": "API Username", "API_PASSWORD": "API Password", "API_RESULT_PARAM": "API Result Parameter", "API_SEARCH_FIELD": "API Search Field", "LDAP_PASSWORD": "LDAP Password", "LDAP_USERNAME": "LDAP Username", "LDAP_BIND_DN": "LDAP Bind DN", "LDAP_SEARCH_BASE": "LDAP Search Base", "LDAP_DOMAIN": "LDAP Domain", "LDAP_PORT": "LDAP Port", "LOCAL_METHOD": "Local Method", "LOGIN_USERNAME": "Login - username", "LOGIN_PASSWORD": "Login - password", "month01": "january", "month02": "february", "month03": "march", "month04": "april", "month05": "may", "month06": "june", "month07": "july", "month08": "august", "month09": "september", "month10": "october", "month11": "november", "month12": "december", "availableActions": "Available actions", "submit": "Submit", "flow": "Flow", "selectFlowToContinue": "Select a flow to continue", "verification_1to1_enabled": "ID Verification", "LDAP_SSL": "LDAP SSL", "ssl_connection": "SSL Connection", "group": "Group", "select_group": "Select a group", "new_related_subject": "New Related Subject", "related_subject": "Related Subject", "relationship": "Relationship", "isVisitor": "Is Visitor", "comments": "Comments", "see_who_relates_to_this_subject": "See who relates to this subject", "subject_relationships": "Subject Relationships", "new_catalog": "New Catalog", "new_location": "New Location", "regex": "Regex", "isPrisonsEnabled": "Prisons Enabled", "prisonerProfileId": "Prisoner Profile ID", "showBelongingsTab": "Show Belongings Tab", "showJudicialFileTab": "Show Judicial File Tab", "belongings": "Belongings", "new_belonging": "New Belonging", "new_belonging_record": "New Belonging Record", "belongingType": "Type", "belongingDescription": "Description", "registrationDate": "Registration Date", "subjectReceptionSignatureDate": "Belongings Hand Over Signature Date", "subjectReceptionSignatureTech": "Belongings Hand Over Signature Tech", "subjectReturnSignatureDate": "Belongings Retrieval Signature Date", "subjectReturnSignatureTech": "Belongings Retrieval Signature Tech", "receptionUserSignatureDate": "Belongings Reception Signature Date", "receptionUserSignatureTech": "Belongings Reception Signature Tech", "receptionUserSignatureNumId": "Belongings Reception User Num ID", "returnUserSignatureDate": "Belongings Return Signature Date", "returnUserSignatureTech": "Belongings Return Signature Tech", "returnUserSignatureNumId": "Belongings Return User Num ID", "new_record": "New Record", "returned": "Returned", "received": "Received", "registered": "Registered", "signature_of": "Signature of the subject", "signature_responsible_of": "Signature of the user responsible", "subject_reception_signature": "Belongings Hand Over Signature", "user_reception_signature": "Belongings Reception Signature", "subject_return_signature": "Belongings Retrieval Signature", "user_return_signature": "Belongings Return Signature", "user_coincidence_signature": "Match confirmation signature", "sign": "Sign", "judicial_file_fields": "Judicial File Fields", "judicialFile": "Judicial File", "new_judicial_file": "New Judicial File", "new_schedule": "New Schedule", "edit_schedule": "Edit Schedule", "roleId": "Role ID", "locationId": "Location ID", "details": "Details", "configuration": "Configuration", "uploaded": "Uploaded", "partner_login_logo_enabled": "Enable login screen custom logo", "partner_topbar_logo_enabled": "Enable top bar custom logo", "entry-exit-control-role-relation-restrictions": "Entry/Exit Control Profile Relation Restrictions", "show-entry-exit-control-role-relation-restrictions": "Show Entry/Exit Control Profile Relation Restrictions", "entry-exit-settings": "Entry/Exit Settings", "new_restriction": "New Restriction", "new_role_relation_restriction": "New Role Relation Restriction", "entryRole": "Entry Role", "subjectWithRole": "Subject with Profile", "relatedRole": "Related Role", "hasToHaveARelatedSubjectWithRole": "Has to have a related subject with profile", "new_transfer": "New Transfer", "new_transfer_auth": "New Transfer Authorization", "edit_transfer_auth": "Edit Transfer Authorization", "execute_transfer_exit": "Execute Transfer Exit", "execute_transfer_entry": "Execute Transfer Entry", "execute": "Execute", "execute_schedule": "Schedule", "remove_schedule": "Remove Schedule", "authCode": "Authorization Code", "authReason": "Transfer Reason", "authRegistrationDate": "Authorization Registration Date", "authExpirationDate": "Authorization Expiration Date", "originLocation": "Origin Location", "destinyLocation": "Destiny Location", "plannedDepartureDateTime": "Planned Departure Date and Time", "actualDepartureDateTime": "Actual Departure Date and Time", "plannedArrivalDateTime": "Planned Arrival Date and Time", "actualArrivalDateTime": "Actual Arrival Date and Time", "listOfPrisoners": "List of Prisoners", "listOfResponsiblePersonel": "List of Responsible Personel", "listOfResponsible": "List of Responsible", "authDate": "Authorization Date", "authUser": "Authorization User", "isCompleted": "Completed", "isCancelled": "Cancelled", "cancelDate": "Cancellation Date", "cancelUser": "Cancellation User", "createdBy": "Created by", "updatedBy": "Updated by", "createdAt": "Created at", "updatedAt": "Updated at", "obtainedAt": "Obtained Date", "signatures": "Signatures", "enter_numId_to_verify": "Enter the ID number to verify otherwise press any technology to identify", "signedOn": "Signed on", "authSignature": "Authorization Signature", "authUserSignatureTech": "Authorization User Signature Tech", "transferExitSignature": "Transfer Exit Signature", "transferEntrySignature": "Transfer Entry Signature", "subjectWhoAuthrizes": "Subject who authorizes", "transferSubject": "Transfer Subject", "transferResponsible": "Transfer Responsible", "cancelSignature": "Cancellation Signature", "cancelUserSignatureTech": "Cancellation User Signature Tech", "subjectWhoCancels": "Subject who cancels", "cancelReason": "Cancellation Reason", "cancelObservation": "Cancellation Observation", "prisonerTabs": "Prisoner <PERSON><PERSON>", "transferAuthConfig": "Transfer Authorization Configuration", "biometricSignaturesConfig": "Biometric Signatures Configuration", "transferAuthExpiration": "Transfer Authorization Expiration", "transferAuthExpirationConfig": "Transfer Authorization Expiration Configuration", "responsibleSubjectsRoles": "Responsible Subjects Profiles", "showTransferAuthDetails": "Show Transfer Authorization Details", "transfer_auth_details_fields": "Transfer Authorization Details Fields", "authorized_subject_roles_to": "Authorized Subject Profiles to", "authorized_subject_roles_for": "Authorized Subject Profiles for", "authorized_subject_roles_to_sign_on": "Authorized Subject Profiles to Sign on", "belongingsReception": "Belongings Reception", "belongingsReturn": "Belongings Return", "authorizeTransferAuths": "Authorize Transfer Authorizations", "cancelTransferAuths": "Cancel Transfer Authorizations", "authorizePrisonerEntryExit": "Authorize Prisoner Entry/Exit", "signPrisonerEntryExit": "Sign Prisoner Entry/Exit", "biometricSignaturesAuthorizedSubjectRoles": "Biometric Signatures Authorized Subject Profiles", "max_inside_time": "Max. Inside Time (hh:mm)", "actual_inside_time": "Inside Time (hh:mm)", "signature": "Signature", "new_authorization": "New Authorization", "edit_authorization": "Edit Authorization", "authStartDateTime": "Authorization Start Date and Time", "authEndDateTime": "Authorization End Date and Time", "entry": "Entry", "exit": "Exit", "showEntryExitAuthDetails": "Show Entry/Exit Authorization Details", "entry_exit_auth_details_fields": "Entry/Exit Authorization Details Fields", "isRequiredWithinSchedule": "Is required within schedule", "requiredSchedule": "Required Schedule", "alertIfAllPerformedWithinSchedule": "Alert if all performed within schedule", "alertIfNotPerformedWithinSchedule": "Alert if not performed within schedule", "alertIfPerformedOutsideSchedule": "Alert if performed outside schedule", "usersToAlert": "Users to alert", "new_job": "New Job", "edit_job": "Edit Job", "time": "Time", "timeIn": "Time In", "limitReached": "Limit reached", "completed_on_time": "Completed", "completed_late": "Completed", "on_time": "Pending", "late": "Pending", "timeInit": "Time Init", "timeEnd": "Time End", "timeInitEnd": "Time Init & End", "dailyWithinTimeRange": "Daily within a time range", "presEnterToAdd": "Press enter to add", "record_already_exists": "Record already exists", "showInMenu": "Show in menu", "triggerInitJobScheduler": "Trigger Init Job Scheduler", "scheduleJob": "Schedule Job", "removeJobSchedule": "Remove Job Schedule", "specificDateTimeStart": "Specific Date and Time to Start Job", "dateTimeStart": "Date and Time to Start Job", "baseIdentifier": "Identifier (Process/URL)", "appRegistry": "Application", "errorMessageToDisplay": "Error message to display", "translations": "Translations", "show_translations": "Show translations", "languageCode": "Language code", "inputTextAreaThreshold": "Input text area threshold", "userSubjectLazyLoadThreshold": "User/Subject lazy load threshold", "up": "Active", "down": "Inactive", "current_password": "Current password", "show_app_flow_types": "Show flow types", "flow_types": "Flow types", "allowCopyPaste": "Allow copy/paste", "datasource_link_field": "Datasource link field", "name_translations": "Name translations", "showFilesTab": "Show Files Tab", "showSubjectFileTypesList": "Show Subject File Types List", "subjectFileTypes": "Subject File Types", "showSubjectFileGroupList": "Show Subject File Group List", "subjectFileGroups": "Subject File Groups", "noSubjectFileGroupsConfigured": "No subject file groups configured, please contact your administrator.", "evidenceFilesTab": "Evidence Tab", "comparatorTab": "Comparator <PERSON>", "showEvidenceFileTypesList": "Show Evidence File Types List", "evidenceFileTypes": "Evidence File Types", "showEvidenceFileGroupList": "Show Evidence File Group List", "evidenceFileGroups": "Evidence File Groups", "noEvidenceFileGroupsConfigured": "No evidence file groups configured, please contact your administrator.", "uploadNewFile": "Upload New File", "no_files_available": "No files available", "alias": "<PERSON><PERSON>", "subjectFileRestrictions": "Subject File Restrictions", "acceptedFileTypes": "Accepted File Types", "acceptedFileTypesToolTip": "Comma-separated list of pattern to restrict the allowed file types. Can be any combination of either the MIME types (such as 'image/*') or the file extensions (such as '.jpg').", "maxFileSize": "<PERSON>. File <PERSON>ze (bytes)", "maxFileSizeTooltip": "Maximum file size in bytes.", "maxResults": "Max. Results", "maxResultsTooltip": "Number between 0-100", "coincidencePercentage": "<PERSON>. Coincidence Percentage", "coincidencePercentageTooltip": "Number between 0-100", "dragAndDropFilesHereToUpload": "Drag and drop files here to upload", "pending": "Pending", "completed": "Completed", "file": "File", "remove": "Remove", "preview": "Preview", "mimeType": "MIME Type", "uploadFile": "Upload File", "selectOption": "Select Option", "isCriminalisticsEnabled": "Criminalistics enabled", "isEnrollByFileEnabled": "Enable enroll by file", "isManageSubjectWithoutVerificationEnabled": "Enable manage subject without verification", "subjectMustExistToAccessThisData": "The subject must be created to access this data", "unavailable": "Unavailable", "liveSamples": "Live samples", "fileSamples": "File samples", "fileAndLiveSamples": "File and live samples", "captureMode": "Sample Capture Mode (Enroll)", "captureModeMatch": "Sample Capture Mode (Match)", "criminalistic_case_details_fields": "Additional information fields for case creation", "selectCase": "Select Case(s)", "selectSubject": "Select Subject(s)", "selectUser": "Select User(s)", "no_created_cases": "No created cases", "no_created_comments_case": "No comments related to this case", "no_created_evidences": "No created evidences", "no_created_coincidences": "No created coincidences", "add_from_evidences": "Please add it from the evidences section", "case_must_exist_to_access_data": "The case must be created to access this data", "isPassEnabled": "Pass enabled", "isClockEnabled": "Clock enabled", "enroll_live_samples": "Enroll live samples", "enroll_file_samples": "Enroll file samples", "index": "Index", "sample_type": "Sample type", "selectImage": "Select image", "UNKNOWN": "Unknown", "LEFT_IRIS": "Left iris", "RIGHT_IRIS": "Right iris", "LEFT_THUMB": "Left thumb", "LEFT_INDEX_FINGER": "Left index finger", "LEFT_MIDDLE_FINGER": "Left middle finger", "LEFT_RING_FINGER": "Left ring finger", "LEFT_LITTLE_FINGER": "Left little finger", "RIGHT_THUMB": "Right thumb", "RIGHT_INDEX_FINGER": "Right index finger", "RIGHT_MIDDLE_FINGER": "Right middle finger", "RIGHT_RING_FINGER": "Right ring finger", "RIGHT_LITTLE_FINGER": "Right little finger", "UP_LEFT_FACE": "Up left face", "UP_FACE": "Up face", "UP_RIGHT_FACE": "Up right face", "RIGHT_FACE": "Right face", "DOWN_RIGHT_FACE": "Down right face", "DOWN_FACE": "Down face", "DOWN_LEFT_FACE": "Down left face", "LEFT_FACE": "Left face", "FRONTAL_FACE": "Frontal face", "LEFT_UPPER_PALM": "Left upper palm", "RIGHT_UPPER_PALM": "Right upper palm", "LEFT_LOWER_PALM": "Left lower palm", "RIGHT_LOWER_PALM": "Right lower palm", "LEFT_LATERAL_PALM": "Left lateral palm", "RIGHT_LATERAL_PALM": "Right lateral palm", "LEFT_INDEX_FINGER_ROLLED": "Left index finger rolled", "LEFT_MIDDLE_FINGER_ROLLED": "Left middle finger rolled", "LEFT_RING_FINGER_ROLLED": "Left ring finger rolled", "LEFT_LITTLE_FINGER_ROLLED": "Left little finger rolled", "RIGHT_INDEX_FINGER_ROLLED": "Right index finger rolled", "RIGHT_MIDDLE_FINGER_ROLLED": "Right middle finger rolled", "RIGHT_RING_FINGER_ROLLED": "Right ring finger rolled", "RIGHT_LITTLE_FINGER_ROLLED": "Right little finger rolled", "LEFT_THUMB_ROLLED": "Left thumb rolled", "RIGHT_THUMB_ROLLED": "Right thumb rolled", "enroll_by_file_mappings": "Enroll by file mappings", "show_mappings": "Show mappings", "items": "Items", "finger": "Finger", "FINGER": "Finger", "new_mapping": "New mapping", "add": "Add", "add_sample_mapping": "Add sample mapping", "select_sample_mapping": "Select sample mapping", "facial_sample": "Facial sample", "fingerprint_sample": "Fingerprint sample", "": ""}, "requiredActionStatus": {"completed_on_time": "Completed", "completed_late": "Completed late", "on_time": "Pending", "late": "Pending not completed"}, "reports": {"export": "Export PDF", "export2": "Export Excel", "tMain2": "of users", "tMain3": "of subjects", "tMain4": "of samples", "tMain5": "Number", "tMain6": "Identifications by technology", "tMain7": "Verifications by technology", "tMain8": "Average", "tMain9": "of pictures", "tUsers1": "User list", "tUsers2": "Users by profile", "tUsers3": "Users by location", "tUsers4": "Users by gender", "tUsers5": "Users and pictures", "tUsers6": "Identifications by technology", "tUsers7": "Verifications by technology", "tSub1": "Subject list", "tSub2": "Subjects by profile", "tSub3": "Subjects by location", "tSub4": "Subjects by gender", "tSub5": "Subjects and pictures", "tSub6": "Identifications by technology", "tSub7": "Verifications by technology", "tV1": "Verification requests by technology", "tV2": "Verification results by technology", "tV3": "Verification requests by location", "tV4": "Verification results by location", "tI1": "1:N requests by technology", "tI2": "1:N results by technology", "tI3": "1:N requests by location", "tI4": "1:N results by location", "tR1": "Average time by technology", "tR2": "Average time by action", "tR3": "Average time by technology", "tR4": "Average time extended", "tTo": "to", "tOf": "of", "tLocations": "locations", "tActions1": "Action list", "tActions2": "Verifications", "tActions3": "Identifications", "tActions4": "Enrollments", "tActions5": "Deletions", "tActions6": "Updates", "tActions7": "<PERSON><PERSON>", "tActions8": "<PERSON><PERSON>", "tActions9": "Pictures", "tActions10": "<PERSON><PERSON>", "tActions11": "Devices", "tActions12": "verifications", "tActions13": "identifications", "tActions14": "subjects enrolled", "tActions15": "subjects deleted", "tActions16": "subjects update", "tActions17": "samples added", "tActions18": "samples deteled", "tActions19": "pictures added", "tActions20": "logins with password", "tActions21": "devices not detected", "tActions22": "Average total time", "tActions23": "Average user time", "tActions24": "Average net time", "tActions25": "Average server time", "tAudit1": "Audited actions", "noData": "No data available", "emissionDate": "Report emission date", "customer": "Customer", "reportType": "Report type", "initDate": "Init date", "endDate": "End date", "actionType": "Action type", "reportTitle1": "Report issuer and receiver", "reportTitle2": "Applied filters", "reportMetadata": "Report generated using Verázial ID Reports", "reportColumn1": "Application", "reportColumn2": "Location", "reportColumn3": "Segment", "reportColumn4": "<PERSON><PERSON>", "reportColumn5": "Action", "reportColumn6": "Executor", "reportColumn7": "Profile", "reportColumn8": "Receiver", "reportColumn9": "Profile", "reportColumn10": "Old value", "reportColumn11": "New value", "reportColumn12": "Date", "reportColumn13": "Action result", "reportColumn14": "Extra data", "reportNameAudit": "Audit trail of actions", "reportIdentifier": "Report identifier"}, "ms_errors": {"0": "Unexpected error", "404": "Not found", "500": "Internal server error", "522": "Connection error", "561": "Unauthorized", "1000": "A database error has occurred", "1001": "Table doesn't exist", "1002": "Tenants cannot be migrated", "2000": "This subject already exists in the data base", "2001": "Subject doesn't exist in the system", "2002": "Number of sessions exceeded", "2003": "User blocked", "2004": "Token expired", "2005": "<PERSON><PERSON> invalid", "2006": "Token used", "2007": "To<PERSON> unavailable", "3000": "This field is required", "3001": "There is a record with the same information", "3002": "It cannot be removed", "3003": "Invalid version format. It must be v{major}.{minor}.{path}, for instance v1.0.0", "4001": "No licenses available", "4002": "License is not enabled", "5001": "Tenant not found", "5002": "Tenant is not enabled", "6000": "External service unexpected error", "6001": "External service method not configured", "6002": "External service method not configured correctly"}, "mservices": {"v-mservice-server-monitoring": "Server Monitoring", "v-mservice-system-settings": "Manager", "v-mservice-tenant-biom-config": "Tenant Biometric Config", "v-mservice-subject": "Subjects", "v-mservice-auth": "<PERSON><PERSON>", "v-mservice-tenant-db-config": "Tenant DB Config", "v-mservice-user": "Users", "v-mservice-apigateway-grpc": "API Gateway gRPC", "v-mservice-biom-mngr": "Biometric Manager", "v-mservice-biom-neurotec13": "Facial & Palm", "v-mservice-cron": "Cron Service", "v-mservice-actions": "Audit Actions", "v-mservice-tenant": "Tenant", "v-mservice-actionsV2": "Audit Actions V2", "v-mservice-storage": "Storage", "v-mservice-biom-neurotec": "Iris & Fingerprint", "neurotechnology9": "Neurotechnology 9", "v-mservice-mail-dispatcher": "Mail Dispatcher", "v-mservice-api-binder": "API Binder", "neurotechnology13": "Neurotechnology 13", "v-mservice-apigateway": "API Gateway", "grafana": "<PERSON><PERSON>", "loki": "<PERSON>", "fluent-bit": "Fluent Bit", "prometheus": "Prometheus", "tempo": "Tempo", "v-mservice-ext-credentials": "External Credentials", "v-mservice-biographic": "Biographic Server", "v-mservice-discovery": "Discovery", "postgres": "Postgres"}, "status": {"created": "Created", "authorized": "Authorized", "cancelled": "Cancelled", "in_progress": "In progress", "completed": "Completed", "expired": "Expired"}, "cron-services": {"roll-call-alerts": "<PERSON> Call Alerts"}, "prime_ng": {"accept": "Yes", "addRule": "Add Rule", "am": "AM", "apply": "Apply", "cancel": "Cancel", "choose": "<PERSON><PERSON>", "chooseDate": "<PERSON>ose <PERSON>", "chooseMonth": "Choose Month", "chooseYear": "Choose Year", "clear": "Clear", "completed": "Completed", "contains": "Contains", "custom": "Custom", "dateAfter": "Date is after", "dateBefore": "Date is before", "dateFormat": "mm/dd/yy", "dateIs": "Date is", "dateIsNot": "Date is not", "dayNames": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"], "dayNamesMin": ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"], "dayNamesShort": ["Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat"], "emptyFilterMessage": "No results found", "emptyMessage": "No available options", "emptySearchMessage": "No results found", "emptySelectionMessage": "No selected item", "endsWith": "Ends with", "equals": "Equals", "fileSizeTypes": ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"], "filter": "Filter", "firstDayOfWeek": 0, "gt": "Greater than", "gte": "Greater than or equal to", "lt": "Less than", "lte": "Less than or equal to", "matchAll": "Match All", "matchAny": "Match Any", "medium": "Medium", "monthNames": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"], "monthNamesShort": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], "nextDecade": "Next Decade", "nextHour": "Next Hour", "nextMinute": "Next Minute", "nextMonth": "Next Month", "nextSecond": "Next Second", "nextYear": "Next Year", "noFilter": "No Filter", "notContains": "Not contains", "notEquals": "Not equals", "now": "Now", "passwordPrompt": "Enter a password", "pending": "Pending", "pm": "PM", "prevDecade": "Previous Decade", "prevHour": "Previous Hour", "prevMinute": "Previous Minute", "prevMonth": "Previous Month", "prevSecond": "Previous Second", "prevYear": "Previous Year", "reject": "No", "removeRule": "Remove Rule", "searchMessage": "{0} results are available", "selectionMessage": "{0} items selected", "showMonthAfterYear": false, "startsWith": "Starts with", "strong": "Strong", "today": "Today", "upload": "Upload", "weak": "Weak", "weekHeader": "Wk", "aria": {"cancelEdit": "Cancel Edit", "close": "Close", "collapseLabel": "Collapse", "collapseRow": "Row Collapsed", "editRow": "Edit Row", "expandLabel": "Expand", "expandRow": "Row Expanded", "falseLabel": "False", "filterConstraint": "Filter Constraint", "filterOperator": "Filter Operator", "firstPageLabel": "First Page", "gridView": "Grid View", "hideFilterMenu": "<PERSON><PERSON> Filter <PERSON>", "jumpToPageDropdownLabel": "Jump to Page Dropdown", "jumpToPageInputLabel": "Jump to Page Input", "lastPageLabel": "Last Page", "listView": "List View", "moveAllToSource": "Move All to Source", "moveAllToTarget": "Move All to Target", "moveBottom": "Move Bottom", "moveDown": "Move Down", "moveToSource": "Move to Source", "moveToTarget": "Move to Target", "moveTop": "Move Top", "moveUp": "Move Up", "navigation": "Navigation", "next": "Next", "nextPageLabel": "Next Page", "nullLabel": "Not Selected", "otpLabel": "Please enter one time password character {0}", "pageLabel": "Page {page}", "passwordHide": "Hide Password", "passwordShow": "Show Password", "previous": "Previous", "previousPageLabel": "Previous Page", "removeLabel": "Remove", "rotateLeft": "Rotate Left", "rotateRight": "Rotate Right", "rowsPerPageLabel": "Rows per page", "saveEdit": "Save Edit", "scrollTop": "Scroll Top", "selectAll": "All items selected", "selectLabel": "Select", "selectRow": "Row Selected", "showFilterMenu": "Show Filter Menu", "slide": "Slide", "slideNumber": "{slideNumber}", "star": "1 star", "stars": "{star} stars", "trueLabel": "True", "unselectAll": "All items unselected", "unselectLabel": "Unselect", "unselectRow": "Row Unselected", "zoomImage": "Zoom Image", "zoomIn": "Zoom In", "zoomOut": "Zoom Out"}}}