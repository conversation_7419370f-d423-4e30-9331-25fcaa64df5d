FROM node:20-alpine AS build
WORKDIR /app
COPY package.json package-lock.json ./
COPY ngx-verazial-ui-lib-1.0.1.tgz ./
COPY drawflow-0.0.50.tgz ./
RUN npm install ngx-verazial-ui-lib-1.0.1.tgz
RUN npm install drawflow-0.0.50.tgz
RUN npm install

COPY . .

RUN npm install -g @angular/cli

RUN npm run build --prod

# Serve Application using Nginx Server
FROM nginx:alpine

# Install jq for JSON processing
RUN apk add --no-cache jq

COPY --from=build /app/dist/verazial-app/browser/ /usr/share/nginx/html

# Copy the default Nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY security-headers.conf /etc/nginx/security-headers.conf

# Create configuration script directly in the image
RUN cat > /usr/local/bin/configure-environment.sh << 'SCRIPT_EOF'
#!/bin/bash

# Script to replace environment variables in the built Angular application
# This script runs inside the Docker container at startup

CONFIG_FILE="/app/config/deployment-config.json"
TARGET_DIR="/usr/share/nginx/html"

echo "Starting environment configuration..."

if [ ! -f "$CONFIG_FILE" ]; then
    echo "Configuration file not found at $CONFIG_FILE"
    echo "Using default environment settings"
    exit 0
fi

echo "Reading configuration from $CONFIG_FILE"

# Read configuration values
GRPC_API_GATEWAY=$(jq -r '.grpcApiGateway' "$CONFIG_FILE")
KONEKTOR_API=$(jq -r '.konektorAPI' "$CONFIG_FILE")
CREDENTIALS_API=$(jq -r '.credentialsAPI' "$CONFIG_FILE")
CREDENTIAL_USER=$(jq -r '.credential_user' "$CONFIG_FILE")
CREDENTIAL_PASSWORD=$(jq -r '.credential_password' "$CONFIG_FILE")

echo "Applying configuration:"
echo "  gRPC API Gateway: $GRPC_API_GATEWAY"
echo "  Konektor API: $KONEKTOR_API"
echo "  Credentials API: $CREDENTIALS_API"
echo "  Credential User: $CREDENTIAL_USER"

# Find all JavaScript files in the target directory
find "$TARGET_DIR" -name "*.js" -type f | while read -r file; do
    echo "Processing file: $file"

    # Replace the environment variables in the JavaScript files
    # Note: These placeholders will be set in the environment.ts file
    sed -i "s|__GRPC_API_GATEWAY__|$GRPC_API_GATEWAY|g" "$file"
    sed -i "s|__KONEKTOR_API__|$KONEKTOR_API|g" "$file"
    sed -i "s|__CREDENTIALS_API__|$CREDENTIALS_API|g" "$file"
    sed -i "s|__CREDENTIAL_USER__|$CREDENTIAL_USER|g" "$file"
    sed -i "s|__CREDENTIAL_PASSWORD__|$CREDENTIAL_PASSWORD|g" "$file"
done

echo "Environment configuration completed successfully!"
SCRIPT_EOF

RUN chmod +x /usr/local/bin/configure-environment.sh

# Create directory for configuration
RUN mkdir -p /app/config

EXPOSE 80

# Create startup script that configures environment and starts nginx
RUN echo '#!/bin/sh' > /usr/local/bin/startup.sh && \
    echo '/usr/local/bin/configure-environment.sh' >> /usr/local/bin/startup.sh && \
    echo 'nginx -g "daemon off;"' >> /usr/local/bin/startup.sh && \
    chmod +x /usr/local/bin/startup.sh

CMD ["/usr/local/bin/startup.sh"]
