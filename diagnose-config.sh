#!/bin/bash

# Diagnostic script to troubleshoot configuration loading issues
# Run this script on your Linux server to diagnose configuration problems

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_color $GREEN "=== Verazial Admin Configuration Diagnostic ==="
echo ""

# Check if Docker Compose is available
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
elif docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    print_color $RED "Docker Compose not found!"
    exit 1
fi

print_color $CYAN "Using Docker Compose command: $DOCKER_COMPOSE"

# 1. Check if deployment-config.json exists and is valid
print_color $YELLOW "1. Checking deployment-config.json..."
if [[ -f "deployment-config.json" ]]; then
    print_color $GREEN "✓ deployment-config.json exists"
    
    if jq empty deployment-config.json 2>/dev/null; then
        print_color $GREEN "✓ deployment-config.json is valid JSON"
        echo "Configuration contents:"
        jq . deployment-config.json
    else
        print_color $RED "✗ deployment-config.json is not valid JSON!"
        echo "File contents:"
        cat deployment-config.json
    fi
else
    print_color $RED "✗ deployment-config.json not found!"
fi

echo ""

# 2. Check container status
print_color $YELLOW "2. Checking container status..."
$DOCKER_COMPOSE ps

echo ""

# 3. Check if configuration file is mounted in container
print_color $YELLOW "3. Checking if config file is mounted in container..."
CONTAINER_NAME=$($DOCKER_COMPOSE ps -q angular-app 2>/dev/null)

if [[ -n "$CONTAINER_NAME" ]]; then
    print_color $CYAN "Container ID: $CONTAINER_NAME"
    
    # Check if config file exists in container
    if docker exec "$CONTAINER_NAME" test -f /app/config/deployment-config.json; then
        print_color $GREEN "✓ Config file is mounted in container"
        echo "Config file contents in container:"
        docker exec "$CONTAINER_NAME" cat /app/config/deployment-config.json
    else
        print_color $RED "✗ Config file NOT found in container at /app/config/deployment-config.json"
        echo "Contents of /app/config/ directory:"
        docker exec "$CONTAINER_NAME" ls -la /app/config/ 2>/dev/null || print_color $RED "Directory /app/config/ does not exist"
    fi
    
    echo ""
    
    # 4. Check if jq is available in container
    print_color $YELLOW "4. Checking if jq is available in container..."
    if docker exec "$CONTAINER_NAME" which jq >/dev/null 2>&1; then
        print_color $GREEN "✓ jq is available in container"
        docker exec "$CONTAINER_NAME" jq --version
    else
        print_color $RED "✗ jq is NOT available in container"
    fi
    
    echo ""
    
    # 5. Check configuration script logs
    print_color $YELLOW "5. Checking configuration script execution..."
    echo "Looking for configuration-related log entries:"
    $DOCKER_COMPOSE logs angular-app | grep -i "configuration\|environment\|config" || print_color $YELLOW "No configuration logs found"
    
    echo ""
    
    # 6. Check if placeholders are still present in JavaScript files
    print_color $YELLOW "6. Checking if placeholders are replaced in JavaScript files..."
    JS_FILES=$(docker exec "$CONTAINER_NAME" find /usr/share/nginx/html -name "*.js" -type f | head -3)
    
    if [[ -n "$JS_FILES" ]]; then
        echo "Checking first few JavaScript files for placeholders:"
        for js_file in $JS_FILES; do
            echo "File: $js_file"
            if docker exec "$CONTAINER_NAME" grep -q "__GRPC_API_GATEWAY__\|__KONEKTOR_API__\|__CREDENTIALS_API__" "$js_file" 2>/dev/null; then
                print_color $RED "✗ Placeholders still found in $js_file"
                echo "Placeholder occurrences:"
                docker exec "$CONTAINER_NAME" grep -n "__.*__" "$js_file" | head -5
            else
                print_color $GREEN "✓ No placeholders found in $js_file"
            fi
            echo ""
        done
    else
        print_color $RED "✗ No JavaScript files found in container"
    fi
    
    echo ""
    
    # 7. Test configuration script manually
    print_color $YELLOW "7. Testing configuration script manually..."
    if docker exec "$CONTAINER_NAME" test -f /usr/local/bin/configure-environment.sh; then
        print_color $GREEN "✓ Configuration script exists"
        echo "Running configuration script manually:"
        docker exec "$CONTAINER_NAME" /usr/local/bin/configure-environment.sh
    else
        print_color $RED "✗ Configuration script not found at /usr/local/bin/configure-environment.sh"
        echo "Looking for script in other locations:"
        docker exec "$CONTAINER_NAME" find / -name "configure-environment.sh" 2>/dev/null || print_color $YELLOW "Script not found anywhere"
    fi
    
else
    print_color $RED "✗ Container not running or not found"
    echo "Available containers:"
    docker ps -a
fi

echo ""

# 8. Check docker-compose.yml volume mounting
print_color $YELLOW "8. Checking docker-compose.yml volume configuration..."
if [[ -f "docker-compose.yml" ]]; then
    print_color $GREEN "✓ docker-compose.yml exists"
    echo "Volume configuration:"
    grep -A 5 -B 5 "volumes:" docker-compose.yml || print_color $YELLOW "No volumes section found"
    echo ""
    echo "Full docker-compose.yml content:"
    cat docker-compose.yml
else
    print_color $RED "✗ docker-compose.yml not found"
fi

echo ""

# 9. Recommendations
print_color $GREEN "=== Recommendations ==="

if [[ ! -f "deployment-config.json" ]]; then
    print_color $YELLOW "• Create deployment-config.json file"
fi

if [[ -n "$CONTAINER_NAME" ]]; then
    if ! docker exec "$CONTAINER_NAME" test -f /app/config/deployment-config.json; then
        print_color $YELLOW "• Check volume mounting in docker-compose.yml"
        print_color $YELLOW "• Ensure the volume line is: ./deployment-config.json:/app/config/deployment-config.json:ro"
    fi
    
    if ! docker exec "$CONTAINER_NAME" which jq >/dev/null 2>&1; then
        print_color $YELLOW "• Rebuild Docker image to include jq"
    fi
    
    if docker exec "$CONTAINER_NAME" grep -q "__.*__" /usr/share/nginx/html/*.js 2>/dev/null; then
        print_color $YELLOW "• Configuration script is not running or not working properly"
        print_color $YELLOW "• Try restarting the container: $DOCKER_COMPOSE restart"
    fi
else
    print_color $YELLOW "• Start the container: $DOCKER_COMPOSE up -d"
fi

echo ""
print_color $CYAN "To fix configuration issues, try:"
print_color $WHITE "1. $DOCKER_COMPOSE down"
print_color $WHITE "2. Edit deployment-config.json"
print_color $WHITE "3. $DOCKER_COMPOSE up -d"
print_color $WHITE "4. $DOCKER_COMPOSE logs -f angular-app"

echo ""
print_color $GREEN "Diagnostic completed!"
