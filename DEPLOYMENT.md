# Verazial Admin - Configurable Deployment System

This deployment system allows you to build Docker images and deploy them with configurable environment variables without rebuilding the application.

## Overview

The system consists of:
1. **Configuration Files**: JSON files containing environment-specific settings
2. **Build Script**: PowerShell script to build and package the application
3. **Deployment Script**: PowerShell script to deploy on target servers
4. **Runtime Configuration**: Automatic environment variable replacement at container startup

## Files Structure

```
├── deployment-config.json          # Default configuration
├── deployment-config.dev.json      # Development environment config
├── deployment-config.demo.json     # Demo environment config
├── build-and-package.ps1          # Build and packaging script
├── deploy.ps1                     # Deployment script
├── docker-compose.yml             # Updated with volume mounting
├── Dockerfile                     # Updated with configuration support
└── scripts/
    └── configure-environment.sh   # Runtime configuration script
```

## Configuration Format

The configuration files use JSON format with the following structure:

```json
{
  "grpcApiGateway": "https://your-grpc-gateway.com",
  "konektorAPI": "https://your-konektor-api.com",
  "credentialsAPI": "https://your-credentials-api.com/api/v1/",
  "credential_user": "your-username",
  "credential_password": "your-password",
  "environment_name": "ENV_NAME",
  "port": "8080"
}
```

### Configurable Variables

- **grpcApiGateway**: URL for the gRPC API Gateway
- **konektorAPI**: URL for the Konektor API
- **credentialsAPI**: URL for the Credentials API
- **credential_user**: Username for API authentication
- **credential_password**: Password for API authentication
- **environment_name**: Environment identifier (DEV, DEMO, PROD, etc.)
- **port**: External port for the application

## Build Process

### 1. Build and Package

Run the build script to create a deployment package:

```powershell
# Build with default configuration
.\build-and-package.ps1

# Build with specific configuration
.\build-and-package.ps1 -ConfigFile deployment-config.dev.json

# Package only (skip build)
.\build-and-package.ps1 -NoBuild

# Show help
.\build-and-package.ps1 -Help
```

This creates a `deployment-package` folder containing:
- `docker-compose.yml`
- `deployment-config.json`
- `*.tar` (Docker images)
- `README.md` (deployment instructions)
- `deploy.ps1` (deployment script)

### 2. What Happens During Build

1. **Docker Build**: Builds the Angular application with placeholder values
2. **Image Packaging**: Saves Docker images as .tar files
3. **Package Creation**: Creates a complete deployment package
4. **Documentation**: Generates deployment instructions

## Deployment Process

### 1. Prepare Target Environment

Copy the `deployment-package` folder to your target server.

### 2. Configure Environment

Edit `deployment-config.json` in the deployment package:

```json
{
  "grpcApiGateway": "https://prod-gatewaygrpc.verazial.com",
  "konektorAPI": "https://prod-konektor.verazial.com",
  "credentialsAPI": "https://prod-pass.verazial.com/api/v1/",
  "credential_user": "prod-user",
  "credential_password": "prod-password",
  "environment_name": "PRODUCTION",
  "port": "80"
}
```

### 3. Deploy

Run the deployment script:

```powershell
# Deploy with default configuration
.\deploy.ps1

# Deploy with specific configuration
.\deploy.ps1 -ConfigFile deployment-config.json

# Deploy without loading images (if already loaded)
.\deploy.ps1 -LoadImages:$false

# Show help
.\deploy.ps1 -Help
```

### 4. What Happens During Deployment

1. **Configuration Validation**: Validates the JSON configuration
2. **Image Loading**: Loads Docker images from .tar files
3. **Environment Setup**: Sets environment variables
4. **Container Startup**: Starts the application with new configuration
5. **Runtime Configuration**: Replaces placeholders in the built application

## How Runtime Configuration Works

1. **Build Time**: The Angular application is built with placeholder values (e.g., `__GRPC_API_GATEWAY__`)
2. **Container Startup**: The configuration script runs automatically
3. **Variable Replacement**: Placeholders in JavaScript files are replaced with actual values
4. **Application Start**: Nginx serves the configured application

## Environment Examples

### Development Environment
```json
{
  "grpcApiGateway": "https://dev-gatewaygrpc.verazial.com",
  "konektorAPI": "https://localhost:8443",
  "credentialsAPI": "https://dev-pass.verazial.com/api/v1/",
  "credential_user": "dev-user",
  "credential_password": "dev-password",
  "environment_name": "DEV",
  "port": "20002"
}
```

### Production Environment
```json
{
  "grpcApiGateway": "https://prod-gatewaygrpc.verazial.com",
  "konektorAPI": "https://prod-konektor.verazial.com",
  "credentialsAPI": "https://prod-pass.verazial.com/api/v1/",
  "credential_user": "prod-user",
  "credential_password": "prod-password",
  "environment_name": "PRODUCTION",
  "port": "443"
}
```

## Troubleshooting

### Common Issues

1. **Configuration file not found**
   - Ensure the configuration file exists in the deployment directory
   - Check the file name matches the parameter

2. **Invalid JSON configuration**
   - Validate JSON syntax using a JSON validator
   - Ensure all required fields are present

3. **Container startup fails**
   - Check logs: `docker compose logs`
   - Verify all API endpoints are accessible
   - Ensure ports are not in use

4. **Application shows placeholder values**
   - Check if the configuration script ran successfully
   - Verify the configuration file is mounted correctly
   - Check container logs for configuration errors

### Useful Commands

```powershell
# Check container status
docker compose ps

# View application logs
docker compose logs -f

# Restart with new configuration
docker compose restart

# Stop the application
docker compose down

# View configuration script logs
docker compose logs angular-app | Select-String "configuration"
```

## Security Considerations

1. **Sensitive Data**: Store passwords and API keys securely
2. **File Permissions**: Restrict access to configuration files
3. **Network Security**: Ensure API endpoints use HTTPS
4. **Container Security**: Keep Docker images updated

## Migration from Previous Setup

If you're migrating from a previous deployment:

1. **Backup**: Save your current configuration values
2. **Create Config**: Create a new configuration file with your values
3. **Test**: Deploy to a test environment first
4. **Deploy**: Use the new deployment process

This system provides a clean separation between build and deployment, allowing you to use the same Docker image across multiple environments with different configurations.
