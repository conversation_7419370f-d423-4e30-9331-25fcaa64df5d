#!/bin/bash

# Deployment script for Verazial Admin on Linux
# This script should be placed in the deployment package and run on the target server

set -e  # Exit on any error

# Default values
CONFIG_FILE="deployment-config.json"
LOAD_IMAGES=true
HELP=false

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show help
show_help() {
    echo "Usage: ./deploy.sh [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -c, --config FILE     Path to the deployment configuration file (default: deployment-config.json)"
    echo "  -n, --no-load-images  Skip loading Docker images from .tar files"
    echo "  -h, --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./deploy.sh                                    # Deploy with default config"
    echo "  ./deploy.sh -c deployment-config.prod.json    # Deploy with specific config"
    echo "  ./deploy.sh --no-load-images                  # Deploy without loading images"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -n|--no-load-images)
            LOAD_IMAGES=false
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_color $RED "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

print_color $GREEN "=== Verazial Admin Deployment Script ==="

# Check if running as root (optional warning)
if [[ $EUID -eq 0 ]]; then
    print_color $YELLOW "Warning: Running as root. Consider using a non-root user with docker permissions."
fi

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    print_color $RED "Docker is not installed or not in PATH!"
    print_color $YELLOW "Please install Docker first: https://docs.docker.com/engine/install/"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_color $RED "Docker daemon is not running or you don't have permission to access it!"
    print_color $YELLOW "Try: sudo systemctl start docker"
    print_color $YELLOW "Or add your user to docker group: sudo usermod -aG docker \$USER"
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_color $RED "Docker Compose is not installed!"
    print_color $YELLOW "Please install Docker Compose: https://docs.docker.com/compose/install/"
    exit 1
fi

# Determine which docker compose command to use
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

print_color $CYAN "Using Docker Compose command: $DOCKER_COMPOSE"

# Check if configuration file exists
if [[ ! -f "$CONFIG_FILE" ]]; then
    print_color $RED "Configuration file '$CONFIG_FILE' not found!"
    print_color $YELLOW "Please ensure the configuration file exists in the current directory."
    exit 1
fi

# Validate configuration file
print_color $YELLOW "Validating configuration file..."
if ! jq empty "$CONFIG_FILE" 2>/dev/null; then
    print_color $RED "Error: Configuration file is not valid JSON!"
    exit 1
fi

# Read and display configuration
print_color $GREEN "Configuration loaded successfully:"
GRPC_GATEWAY=$(jq -r '.grpcApiGateway' "$CONFIG_FILE")
KONEKTOR_API=$(jq -r '.konektorAPI' "$CONFIG_FILE")
CREDENTIALS_API=$(jq -r '.credentialsAPI' "$CONFIG_FILE")
CREDENTIAL_USER=$(jq -r '.credential_user' "$CONFIG_FILE")
PORT=$(jq -r '.port' "$CONFIG_FILE")
ENV_NAME=$(jq -r '.environment_name // "UNKNOWN"' "$CONFIG_FILE")

print_color $CYAN "  - Environment: $ENV_NAME"
print_color $CYAN "  - gRPC API Gateway: $GRPC_GATEWAY"
print_color $CYAN "  - Konektor API: $KONEKTOR_API"
print_color $CYAN "  - Credentials API: $CREDENTIALS_API"
print_color $CYAN "  - Credential User: $CREDENTIAL_USER"
print_color $CYAN "  - Port: $PORT"

# Set environment variable for port
export PORT="$PORT"
print_color $YELLOW "Set PORT environment variable to: $PORT"

# Load Docker images if requested
if [[ "$LOAD_IMAGES" == true ]]; then
    print_color $YELLOW "Loading Docker images..."
    
    # Find all .tar files
    TAR_FILES=(*.tar)
    
    if [[ ${#TAR_FILES[@]} -eq 0 ]] || [[ ! -f "${TAR_FILES[0]}" ]]; then
        print_color $RED "No .tar files found in current directory!"
        print_color $YELLOW "Available files:"
        ls -la
        exit 1
    fi
    
    for tar_file in "${TAR_FILES[@]}"; do
        if [[ -f "$tar_file" ]]; then
            print_color $CYAN "Loading image from $tar_file..."
            if docker load -i "$tar_file"; then
                print_color $GREEN "Successfully loaded $tar_file"
            else
                print_color $RED "Failed to load $tar_file"
                exit 1
            fi
        fi
    done
else
    print_color $YELLOW "Skipping image loading (--no-load-images specified)"
fi

# Check if docker-compose.yml exists
if [[ ! -f "docker-compose.yml" ]]; then
    print_color $RED "docker-compose.yml not found in current directory!"
    exit 1
fi

# Check if port is available
if command -v netstat &> /dev/null; then
    if netstat -tuln | grep -q ":$PORT "; then
        print_color $YELLOW "Warning: Port $PORT appears to be in use"
        print_color $YELLOW "Continuing anyway - Docker Compose will handle port conflicts"
    fi
fi

# Stop existing containers
print_color $YELLOW "Stopping existing containers..."
$DOCKER_COMPOSE down || true

# Start the application
print_color $YELLOW "Starting Verazial Admin application..."
if $DOCKER_COMPOSE up -d; then
    echo ""
    print_color $GREEN "=== Deployment Completed Successfully! ==="
    echo ""
    print_color $CYAN "Application is now running on port: $PORT"
    print_color $CYAN "Access URL: http://localhost:$PORT"
    print_color $CYAN "External access: http://$(hostname -I | awk '{print $1}'):$PORT"
    echo ""
    print_color $YELLOW "Useful commands:"
    print_color $YELLOW "  Check status: $DOCKER_COMPOSE ps"
    print_color $YELLOW "  View logs: $DOCKER_COMPOSE logs -f"
    print_color $YELLOW "  Stop application: $DOCKER_COMPOSE down"
    print_color $YELLOW "  Restart: $DOCKER_COMPOSE restart"
    echo ""
    
    # Wait a moment and check if containers are running
    sleep 3
    print_color $YELLOW "Container status:"
    $DOCKER_COMPOSE ps
    
else
    print_color $RED "Deployment failed! Check the logs with: $DOCKER_COMPOSE logs"
    exit 1
fi
