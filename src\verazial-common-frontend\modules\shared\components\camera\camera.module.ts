import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { FloatLabelModule } from 'primeng/floatlabel';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { CameraComponent } from './camera/camera.component';
import { InactivityMonitorModule } from '../inactivity-monitor/inactivity-monitor.module';
import { TieredMenuModule } from 'primeng/tieredmenu';

@NgModule({
  declarations: [
    CameraComponent
  ],
  imports: [
    /* Angular Modules */
    CommonModule,
    /* Forms */
    ReactiveFormsModule,
    FormsModule,
    /* Translate */
    TranslateModule,
    /* PrimeNG Modules */
    DialogModule,
    ButtonModule,
    DropdownModule,
    ProgressSpinnerModule,
    FloatLabelModule,
    ConfirmDialogModule,
    ToastModule,
    TieredMenuModule,
    /* Custom Modules */
    InactivityMonitorModule,
  ],
  exports: [
    CameraComponent
  ]
})
export class CameraModule { }
